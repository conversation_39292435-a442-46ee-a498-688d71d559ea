import Link from 'next/link'
import { prisma } from '@/lib/prisma'

async function getDashboardData() {
  const [partners, products, recentTransactions, categories, allTransactions] = await Promise.all([
    prisma.partner.findMany({
      where: { isActive: true },
      orderBy: { name: 'asc' }
    }),
    prisma.product.findMany({
      include: { category: true },
      orderBy: { name: 'asc' }
    }),
    prisma.transaction.findMany({
      include: { partner: true },
      orderBy: { date: 'desc' },
      take: 5
    }),
    prisma.productCategory.findMany({
      include: { _count: { select: { products: true } } },
      orderBy: { name: 'asc' }
    }),
    prisma.transaction.findMany({
      select: { type: true, totalAmount: true, cashIn: true, cashOut: true }
    })
  ])

  // Calculate total inventory value
  const totalInventoryValue = products.reduce((sum, product) =>
    sum + (product.currentStock * product.costPrice), 0
  )

  // Calculate total potential revenue
  const totalPotentialRevenue = products.reduce((sum, product) =>
    sum + (product.currentStock * product.salePrice), 0
  )

  // Calculate financial metrics
  const totalSales = allTransactions
    .filter(t => t.type === 'SALE')
    .reduce((sum, t) => sum + t.totalAmount, 0)

  const totalExpenses = allTransactions
    .filter(t => t.type === 'EXPENSE')
    .reduce((sum, t) => sum + t.totalAmount, 0)

  const totalPurchases = allTransactions
    .filter(t => t.type === 'PURCHASE')
    .reduce((sum, t) => sum + t.totalAmount, 0)

  const netProfit = totalSales - totalExpenses - totalPurchases
  const profitPerPartner = netProfit / 5 // 5 partners with equal stakes

  const totalCashIn = allTransactions.reduce((sum, t) => sum + t.cashIn, 0)
  const totalCashOut = allTransactions.reduce((sum, t) => sum + t.cashOut, 0)
  const cashBalance = totalCashIn - totalCashOut

  return {
    partners,
    products,
    recentTransactions,
    categories,
    totalInventoryValue,
    totalPotentialRevenue,
    totalProducts: products.length,
    lowStockProducts: products.filter(p => p.currentStock <= p.minStock).length,
    financials: {
      totalSales,
      totalExpenses,
      totalPurchases,
      netProfit,
      profitPerPartner,
      cashBalance,
      totalTransactions: allTransactions.length
    }
  }
}

export default async function Dashboard() {
  const data = await getDashboardData()

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🎴 Pokemon Card Business Tracker
          </h1>
          <p className="text-gray-600">
            Managing your 5-partner Pokemon card business with 20% stakes each
          </p>
        </div>

        {/* Financial Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <span className="text-2xl">💰</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Sales</p>
                <p className="text-2xl font-semibold text-green-600">
                  ${data.financials.totalSales.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <span className="text-2xl">💸</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Expenses</p>
                <p className="text-2xl font-semibold text-red-600">
                  ${data.financials.totalExpenses.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`p-2 rounded-lg ${data.financials.netProfit >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                <span className="text-2xl">{data.financials.netProfit >= 0 ? '📈' : '📉'}</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Net Profit</p>
                <p className={`text-2xl font-semibold ${data.financials.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${data.financials.netProfit.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-2xl">🏦</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Cash Balance</p>
                <p className={`text-2xl font-semibold ${data.financials.cashBalance >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${data.financials.cashBalance.toFixed(2)}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Business Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-2xl">👥</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Partners</p>
                <p className="text-2xl font-semibold text-gray-900">{data.partners.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <span className="text-2xl">📦</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Inventory Value</p>
                <p className="text-2xl font-semibold text-gray-900">
                  ${data.totalInventoryValue.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <span className="text-2xl">🎴</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Products</p>
                <p className="text-2xl font-semibold text-gray-900">{data.totalProducts}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <span className="text-2xl">⚠️</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Low Stock Items</p>
                <p className="text-2xl font-semibold text-gray-900">{data.lowStockProducts}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Profit Sharing */}
        <div className="bg-white rounded-lg shadow mb-8 p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">💎 Profit Sharing (20% per Partner)</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className={`text-3xl font-bold ${data.financials.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                ${data.financials.profitPerPartner.toFixed(2)}
              </div>
              <div className="text-gray-600">Profit per Partner</div>
              <div className="text-sm text-gray-500 mt-1">
                {data.financials.netProfit >= 0 ? 'Ready for distribution' : 'Loss to cover'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">{data.financials.totalTransactions}</div>
              <div className="text-gray-600">Total Transactions</div>
              <div className="text-sm text-gray-500 mt-1">All business activity</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">
                ${(data.totalPotentialRevenue - data.totalInventoryValue).toFixed(2)}
              </div>
              <div className="text-gray-600">Potential Profit</div>
              <div className="text-sm text-gray-500 mt-1">If all inventory sold</div>
            </div>
          </div>
        </div>

        {/* Navigation Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Link href="/partners" className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">👥</span>
              <h3 className="text-xl font-semibold text-gray-900">Partners</h3>
            </div>
            <p className="text-gray-600 mb-4">Manage your 5 business partners and their 20% stakes</p>
            <div className="text-sm text-blue-600 font-medium">View Partners →</div>
          </Link>

          <Link href="/categories" className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">📂</span>
              <h3 className="text-xl font-semibold text-gray-900">Categories</h3>
            </div>
            <p className="text-gray-600 mb-4">Manage product categories and profit-sharing arrangements</p>
            <div className="text-sm text-blue-600 font-medium">View Categories →</div>
          </Link>

          <Link href="/products" className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">🎴</span>
              <h3 className="text-xl font-semibold text-gray-900">Products</h3>
            </div>
            <p className="text-gray-600 mb-4">Manage Pokemon cards, booster packs, and accessories</p>
            <div className="text-sm text-blue-600 font-medium">View Products →</div>
          </Link>

          <Link href="/transactions" className="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow">
            <div className="flex items-center mb-4">
              <span className="text-3xl mr-3">💳</span>
              <h3 className="text-xl font-semibold text-gray-900">Transactions</h3>
            </div>
            <p className="text-gray-600 mb-4">Record sales, purchases, and business expenses</p>
            <div className="text-sm text-blue-600 font-medium">View Transactions →</div>
          </Link>
        </div>

        {/* Recent Activity & Quick Info */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Transactions */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Recent Transactions</h3>
            </div>
            <div className="p-6">
              {data.recentTransactions.length > 0 ? (
                <div className="space-y-4">
                  {data.recentTransactions.map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900">{transaction.description}</p>
                        <p className="text-sm text-gray-600">
                          {transaction.partner?.name || 'System'} • {new Date(transaction.date).toLocaleDateString()}
                        </p>
                      </div>
                      <div className={`font-semibold ${
                        transaction.type === 'SALE' ? 'text-green-600' :
                        transaction.type === 'EXPENSE' ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        ${transaction.totalAmount.toFixed(2)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-4">No transactions yet</p>
              )}
            </div>
          </div>

          {/* Product Categories */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Product Categories</h3>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {data.categories.map((category) => (
                  <div key={category.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-gray-900">{category.name}</p>
                      <p className="text-sm text-gray-600">{category.description}</p>
                    </div>
                    <div className="text-sm font-semibold text-gray-600">
                      {category._count.products} items
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

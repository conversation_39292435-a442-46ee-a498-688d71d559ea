import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    const category = await prisma.productCategory.findUnique({
      where: { id },
      include: {
        products: {
          select: {
            id: true,
            name: true,
            sku: true,
            currentStock: true,
            costPrice: true,
            salePrice: true,
            isActive: true
          }
        }
      }
    })

    if (!category) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(category)
  } catch (error) {
    console.error('Error fetching category:', error)
    return NextResponse.json(
      { error: 'Failed to fetch category' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    const { 
      name, 
      description, 
      partner1Share, 
      partner2Share, 
      partner3Share, 
      partner4Share, 
      partner5Share,
      isActive 
    } = body

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400 }
      )
    }

    // Validate profit shares add up to 100%
    const totalShare = (partner1Share || 20) + (partner2Share || 20) + (partner3Share || 20) + (partner4Share || 20) + (partner5Share || 20)
    if (Math.abs(totalShare - 100) > 0.01) {
      return NextResponse.json(
        { error: 'Partner profit shares must add up to 100%' },
        { status: 400 }
      )
    }

    // Check if category exists
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id }
    })
    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    // Check if name is taken by another category
    if (name !== existingCategory.name) {
      const nameExists = await prisma.productCategory.findUnique({
        where: { name }
      })
      if (nameExists) {
        return NextResponse.json(
          { error: 'Category name already exists' },
          { status: 400 }
        )
      }
    }

    // Update the category
    const updatedCategory = await prisma.productCategory.update({
      where: { id },
      data: {
        name,
        description: description || null,
        partner1Share: partner1Share || 20.0,
        partner2Share: partner2Share || 20.0,
        partner3Share: partner3Share || 20.0,
        partner4Share: partner4Share || 20.0,
        partner5Share: partner5Share || 20.0,
        isActive: isActive !== undefined ? isActive : true
      },
      include: {
        products: {
          select: {
            id: true,
            name: true,
            currentStock: true,
            isActive: true
          }
        }
      }
    })

    return NextResponse.json(updatedCategory)
  } catch (error) {
    console.error('Error updating category:', error)
    return NextResponse.json(
      { error: 'Failed to update category' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params

    // Check if category exists
    const existingCategory = await prisma.productCategory.findUnique({
      where: { id },
      include: {
        products: true
      }
    })

    if (!existingCategory) {
      return NextResponse.json(
        { error: 'Category not found' },
        { status: 404 }
      )
    }

    // Check if category has products
    if (existingCategory.products.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete category with existing products. Please move or delete products first.' },
        { status: 400 }
      )
    }

    // Delete the category
    await prisma.productCategory.delete({
      where: { id }
    })

    return NextResponse.json({ message: 'Category deleted successfully' })
  } catch (error) {
    console.error('Error deleting category:', error)
    return NextResponse.json(
      { error: 'Failed to delete category' },
      { status: 500 }
    )
  }
}

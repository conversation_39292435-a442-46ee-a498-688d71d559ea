// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// Partners in the business (5 friends with 20% stake each)
model Partner {
  id        String   @id @default(cuid())
  name      String   @unique
  email     String?  @unique
  phone     String?
  stake     Float    @default(0.20) // 20% stake for each partner
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  transactions Transaction[]
  contributions Contribution[]

  @@map("partners")
}

// Product categories (Pokemon cards, accessories, etc.)
model ProductCategory {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?

  // Partner profit sharing percentages (must add up to 100%)
  partner1Share Float   @default(20.0) // Partner 1's profit share percentage
  partner2Share Float   @default(20.0) // Partner 2's profit share percentage
  partner3Share Float   @default(20.0) // Partner 3's profit share percentage
  partner4Share Float   @default(20.0) // Partner 4's profit share percentage
  partner5Share Float   @default(20.0) // Partner 5's profit share percentage

  // Category-specific settings
  isActive      Boolean  @default(true)

  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  products Product[]

  @@map("product_categories")
}

// Products in inventory
model Product {
  id          String   @id @default(cuid())
  name        String
  description String?
  sku         String?  @unique
  categoryId  String

  // Pricing
  costPrice   Float    // What we paid for it
  salePrice   Float    // What we sell it for

  // Inventory
  currentStock Int     @default(0)
  minStock     Int     @default(0)

  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  category         ProductCategory @relation(fields: [categoryId], references: [id])
  transactionItems TransactionItem[]
  inventoryLogs    InventoryLog[]

  @@map("products")
}

// Transaction types
enum TransactionType {
  SALE        // Revenue from selling products
  PURCHASE    // Buying inventory
  EXPENSE     // Business expenses (market fees, supplies, etc.)
  CONTRIBUTION // Partner contributions to business
  DISTRIBUTION // Profit distribution to partners
}

// Main transactions table for cash accounting
model Transaction {
  id          String          @id @default(cuid())
  type        TransactionType
  description String
  totalAmount Float           // Total transaction amount
  date        DateTime        @default(now())
  partnerId   String?         // Which partner handled this transaction

  // Cash accounting fields
  cashIn      Float           @default(0) // Money coming in
  cashOut     Float           @default(0) // Money going out

  notes       String?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt

  // Relations
  partner Partner?           @relation(fields: [partnerId], references: [id])
  items   TransactionItem[]

  @@map("transactions")
}

// Individual items within a transaction
model TransactionItem {
  id            String  @id @default(cuid())
  transactionId String
  productId     String?  // Optional for freeform products
  quantity      Int
  unitPrice     Float   // Price per unit at time of transaction (can be overridden)
  totalPrice    Float   // quantity * unitPrice

  // For freeform products (when productId is null)
  freeformName        String? // Custom product name
  freeformDescription String? // Custom product description
  freeformSku         String? // Custom SKU if needed

  // Trade-in information (for sales with trade-ins)
  isTradeIn           Boolean @default(false)
  tradeInValue        Float?  // Value given for trade-in item
  tradeInCondition    String? // Condition of trade-in item
  tradeInNotes        String? // Additional notes about trade-in

  createdAt     DateTime @default(now())

  // Relations
  transaction Transaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  product     Product?    @relation(fields: [productId], references: [id]) // Optional for freeform

  @@map("transaction_items")
}

// Track partner contributions to the business
model Contribution {
  id          String   @id @default(cuid())
  partnerId   String
  amount      Float
  description String
  date        DateTime @default(now())
  createdAt   DateTime @default(now())

  // Relations
  partner Partner @relation(fields: [partnerId], references: [id])

  @@map("contributions")
}

// Inventory tracking for stock management
model InventoryLog {
  id          String   @id @default(cuid())
  productId   String
  changeType  String   // "IN", "OUT", "ADJUSTMENT"
  quantity    Int      // Positive for additions, negative for removals
  reason      String   // "PURCHASE", "SALE", "DAMAGE", "ADJUSTMENT"
  date        DateTime @default(now())
  notes       String?

  // Relations
  product Product @relation(fields: [productId], references: [id])

  @@map("inventory_logs")
}

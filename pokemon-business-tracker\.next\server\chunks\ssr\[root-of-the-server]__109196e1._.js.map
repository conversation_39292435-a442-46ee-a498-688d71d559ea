{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/SpearheadApp/pokemon-business-tracker/src/app/transactions/new/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\n\ninterface Partner {\n  id: string\n  name: string\n}\n\ninterface Product {\n  id: string\n  name: string\n  salePrice: number\n  currentStock: number\n  category: {\n    name: string\n  }\n}\n\ninterface TransactionItem {\n  productId?: string // Optional for freeform products\n  quantity: number\n  unitPrice: number\n\n  // For freeform products\n  freeformName?: string\n  freeformDescription?: string\n  freeformSku?: string\n\n  // Trade-in information\n  isTradeIn?: boolean\n  tradeInValue?: number\n  tradeInCondition?: string\n  tradeInNotes?: string\n}\n\ninterface FreeformProduct {\n  name: string\n  description: string\n  sku: string\n  unitPrice: number\n}\n\nexport default function NewTransactionPage() {\n  const router = useRouter()\n  const [partners, setPartners] = useState<Partner[]>([])\n  const [products, setProducts] = useState<Product[]>([])\n  const [loading, setLoading] = useState(true)\n  const [submitting, setSubmitting] = useState(false)\n\n  // Form state\n  const [transactionType, setTransactionType] = useState<'SALE' | 'PURCHASE' | 'EXPENSE'>('SALE')\n  const [description, setDescription] = useState('')\n  const [partnerId, setPartnerId] = useState('')\n  const [notes, setNotes] = useState('')\n  const [items, setItems] = useState<TransactionItem[]>([])\n  const [tradeInItems, setTradeInItems] = useState<TransactionItem[]>([])\n  const [expenseAmount, setExpenseAmount] = useState('')\n\n  // UI state\n  const [showFreeformProduct, setShowFreeformProduct] = useState(false)\n  const [showTradeInSection, setShowTradeInSection] = useState(false)\n\n  useEffect(() => {\n    fetchData()\n  }, [])\n\n  const fetchData = async () => {\n    try {\n      const [partnersRes, productsRes] = await Promise.all([\n        fetch('/api/partners'),\n        fetch('/api/products')\n      ])\n      \n      const partnersData = await partnersRes.json()\n      const productsData = await productsRes.json()\n      \n      setPartners(partnersData)\n      setProducts(productsData)\n    } catch (error) {\n      console.error('Error fetching data:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const addItem = () => {\n    setItems([...items, { productId: '', quantity: 1, unitPrice: 0 }])\n  }\n\n  const addFreeformItem = () => {\n    setItems([...items, {\n      quantity: 1,\n      unitPrice: 0,\n      freeformName: '',\n      freeformDescription: '',\n      freeformSku: ''\n    }])\n    setShowFreeformProduct(false)\n  }\n\n  const addTradeInItem = () => {\n    setTradeInItems([...tradeInItems, {\n      quantity: 1,\n      unitPrice: 0,\n      isTradeIn: true,\n      tradeInValue: 0,\n      tradeInCondition: 'Good',\n      freeformName: '',\n      freeformDescription: ''\n    }])\n  }\n\n  const removeItem = (index: number) => {\n    setItems(items.filter((_, i) => i !== index))\n  }\n\n  const removeTradeInItem = (index: number) => {\n    setTradeInItems(tradeInItems.filter((_, i) => i !== index))\n  }\n\n  const updateItem = (index: number, field: keyof TransactionItem, value: string | number) => {\n    const updatedItems = [...items]\n    updatedItems[index] = { ...updatedItems[index], [field]: value }\n\n    // Auto-populate price when product is selected\n    if (field === 'productId' && value) {\n      const product = getProductById(value as string)\n      if (product) {\n        updatedItems[index].unitPrice = product.salePrice\n      }\n    }\n\n    setItems(updatedItems)\n  }\n\n  const updateTradeInItem = (index: number, field: keyof TransactionItem, value: string | number) => {\n    const updatedItems = [...tradeInItems]\n    updatedItems[index] = { ...updatedItems[index], [field]: value }\n    setTradeInItems(updatedItems)\n  }\n\n  const getProductById = (productId: string) => {\n    return products.find(p => p.id === productId)\n  }\n\n  const calculateTotal = () => {\n    if (transactionType === 'EXPENSE') {\n      return parseFloat(expenseAmount) || 0\n    }\n    const itemsTotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)\n    const tradeInTotal = tradeInItems.reduce((sum, item) => sum + (item.tradeInValue || 0), 0)\n    return itemsTotal - tradeInTotal // Subtract trade-in value from total\n  }\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setSubmitting(true)\n\n    try {\n      // Combine regular items and trade-in items\n      const allItems = [\n        ...items.filter(item => item.productId || item.freeformName), // Include freeform items\n        ...tradeInItems.map(item => ({ ...item, isTradeIn: true }))\n      ]\n\n      const transactionData = {\n        type: transactionType,\n        description,\n        partnerId: partnerId || null,\n        notes,\n        totalAmount: calculateTotal(),\n        items: transactionType !== 'EXPENSE' ? allItems : []\n      }\n\n      const response = await fetch('/api/transactions', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(transactionData),\n      })\n\n      if (response.ok) {\n        router.push('/transactions')\n      } else {\n        throw new Error('Failed to create transaction')\n      }\n    } catch (error) {\n      console.error('Error creating transaction:', error)\n      alert('Error creating transaction. Please try again.')\n    } finally {\n      setSubmitting(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 p-6 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-6\">\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <Link href=\"/transactions\" className=\"text-blue-600 hover:text-blue-800 mb-2 inline-block\">\n            ← Back to Transactions\n          </Link>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            💳 New Transaction\n          </h1>\n          <p className=\"text-gray-600\">\n            Record a sale, purchase, or business expense\n          </p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Transaction Type */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Transaction Type</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              {[\n                { value: 'SALE', label: 'Sale', icon: '💰', desc: 'Record a sale to customers' },\n                { value: 'PURCHASE', label: 'Purchase', icon: '🛒', desc: 'Record inventory purchases' },\n                { value: 'EXPENSE', label: 'Expense', icon: '💸', desc: 'Record business expenses' }\n              ].map((type) => (\n                <label key={type.value} className=\"cursor-pointer\">\n                  <input\n                    type=\"radio\"\n                    name=\"transactionType\"\n                    value={type.value}\n                    checked={transactionType === type.value}\n                    onChange={(e) => setTransactionType(e.target.value as any)}\n                    className=\"sr-only\"\n                  />\n                  <div className={`border-2 rounded-lg p-4 text-center transition-colors ${\n                    transactionType === type.value \n                      ? 'border-blue-500 bg-blue-50' \n                      : 'border-gray-200 hover:border-gray-300'\n                  }`}>\n                    <div className=\"text-2xl mb-2\">{type.icon}</div>\n                    <div className=\"font-medium text-gray-900\">{type.label}</div>\n                    <div className=\"text-sm text-gray-600\">{type.desc}</div>\n                  </div>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* Basic Information */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Transaction Details</h2>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Description *\n                </label>\n                <input\n                  type=\"text\"\n                  required\n                  value={description}\n                  onChange={(e) => setDescription(e.target.value)}\n                  placeholder={`Enter ${transactionType.toLowerCase()} description`}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n                />\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Partner\n                </label>\n                <select\n                  value={partnerId}\n                  onChange={(e) => setPartnerId(e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n                >\n                  <option value=\"\">Select partner (optional)</option>\n                  {partners.map((partner) => (\n                    <option key={partner.id} value={partner.id}>\n                      {partner.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n            </div>\n            <div className=\"mt-4\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Notes\n              </label>\n              <textarea\n                value={notes}\n                onChange={(e) => setNotes(e.target.value)}\n                rows={3}\n                placeholder=\"Additional notes (optional)\"\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n              />\n            </div>\n          </div>\n\n          {/* Items or Expense Amount */}\n          {transactionType === 'EXPENSE' ? (\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Expense Amount</h2>\n              <div className=\"max-w-xs\">\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  Amount *\n                </label>\n                <div className=\"relative\">\n                  <span className=\"absolute left-3 top-2 text-gray-500\">$</span>\n                  <input\n                    type=\"number\"\n                    step=\"0.01\"\n                    min=\"0\"\n                    required\n                    value={expenseAmount}\n                    onChange={(e) => setExpenseAmount(e.target.value)}\n                    placeholder=\"0.00\"\n                    className=\"w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n                  />\n                </div>\n              </div>\n            </div>\n          ) : (\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">Items</h2>\n                <div className=\"flex space-x-2\">\n                  <button\n                    type=\"button\"\n                    onClick={addItem}\n                    className=\"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\"\n                  >\n                    Add Product\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={addFreeformItem}\n                    className=\"bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors\"\n                  >\n                    Add Custom Item\n                  </button>\n                  {transactionType === 'SALE' && (\n                    <button\n                      type=\"button\"\n                      onClick={() => setShowTradeInSection(!showTradeInSection)}\n                      className=\"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors\"\n                    >\n                      {showTradeInSection ? 'Hide' : 'Add'} Trade-ins\n                    </button>\n                  )}\n                </div>\n              </div>\n              \n              {items.length === 0 ? (\n                <p className=\"text-gray-500 text-center py-8\">\n                  No items added yet. Click \"Add Product\" or \"Add Custom Item\" to get started.\n                </p>\n              ) : (\n                <div className=\"space-y-4\">\n                  {items.map((item, index) => (\n                    <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\n                      <div className=\"flex justify-between items-start mb-4\">\n                        <h3 className=\"text-sm font-medium text-gray-700\">\n                          {item.freeformName ? 'Custom Item' : 'Product Item'} #{index + 1}\n                        </h3>\n                        <button\n                          type=\"button\"\n                          onClick={() => removeItem(index)}\n                          className=\"text-red-600 hover:text-red-800\"\n                        >\n                          Remove\n                        </button>\n                      </div>\n\n                      {item.freeformName ? (\n                        // Freeform product fields\n                        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                              Product Name *\n                            </label>\n                            <input\n                              type=\"text\"\n                              value={item.freeformName || ''}\n                              onChange={(e) => updateItem(index, 'freeformName', e.target.value)}\n                              placeholder=\"e.g., Charizard Base Set\"\n                              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n                            />\n                          </div>\n                          <div>\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                              SKU (Optional)\n                            </label>\n                            <input\n                              type=\"text\"\n                              value={item.freeformSku || ''}\n                              onChange={(e) => updateItem(index, 'freeformSku', e.target.value)}\n                              placeholder=\"e.g., CHAR-BS-001\"\n                              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n                            />\n                          </div>\n                          <div className=\"md:col-span-2\">\n                            <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                              Description (Optional)\n                            </label>\n                            <input\n                              type=\"text\"\n                              value={item.freeformDescription || ''}\n                              onChange={(e) => updateItem(index, 'freeformDescription', e.target.value)}\n                              placeholder=\"Brief description of the item\"\n                              className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n                            />\n                          </div>\n                        </div>\n                      ) : (\n                        // Regular product selection\n                        <div className=\"mb-4\">\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                            Product\n                          </label>\n                          <select\n                            value={item.productId || ''}\n                            onChange={(e) => updateItem(index, 'productId', e.target.value)}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n                          >\n                            <option value=\"\">Select product</option>\n                            {products.map((product) => (\n                              <option key={product.id} value={product.id}>\n                                {product.name} ({product.category.name}) - Stock: {product.currentStock}\n                              </option>\n                            ))}\n                          </select>\n                        </div>\n                      )}\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                            Quantity\n                          </label>\n                          <input\n                            type=\"number\"\n                            min=\"1\"\n                            value={item.quantity}\n                            onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                            Unit Price *\n                            {item.productId && (\n                              <span className=\"text-xs text-gray-500 ml-1\">\n                                (Default: ${getProductById(item.productId)?.salePrice.toFixed(2) || '0.00'})\n                              </span>\n                            )}\n                          </label>\n                          <div className=\"relative\">\n                            <span className=\"absolute left-3 top-2 text-gray-500\">$</span>\n                            <input\n                              type=\"number\"\n                              step=\"0.01\"\n                              min=\"0\"\n                              value={item.unitPrice}\n                              onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}\n                              placeholder={item.productId ? getProductById(item.productId)?.salePrice.toFixed(2) : '0.00'}\n                              className=\"w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white\"\n                            />\n                          </div>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                            Total\n                          </label>\n                          <div className=\"px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-900 font-medium\">\n                            ${(item.quantity * item.unitPrice).toFixed(2)}\n                          </div>\n                        </div>\n                      </div>\n\n                      {item.productId && getProductById(item.productId) && (\n                        <div className=\"mt-2 text-sm text-gray-600 bg-blue-50 p-2 rounded\">\n                          <strong>Product Info:</strong> Stock: {getProductById(item.productId)!.currentStock} available\n                          {item.unitPrice !== getProductById(item.productId)!.salePrice && (\n                            <span className=\"ml-4 text-orange-600\">\n                              (Price overridden from ${getProductById(item.productId)!.salePrice.toFixed(2)})\n                            </span>\n                          )}\n                        </div>\n                      )}\n\n                      {item.freeformName && (\n                        <div className=\"mt-2 text-sm text-gray-600 bg-green-50 p-2 rounded\">\n                          <strong>Custom Item:</strong> {item.freeformName}\n                          {item.freeformDescription && ` - ${item.freeformDescription}`}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Trade-in Section */}\n          {transactionType === 'SALE' && showTradeInSection && (\n            <div className=\"bg-white rounded-lg shadow p-6\">\n              <div className=\"flex justify-between items-center mb-4\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">Trade-ins</h2>\n                <button\n                  type=\"button\"\n                  onClick={addTradeInItem}\n                  className=\"bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors\"\n                >\n                  Add Trade-in Item\n                </button>\n              </div>\n\n              {tradeInItems.length === 0 ? (\n                <p className=\"text-gray-500 text-center py-8\">\n                  No trade-in items added yet. Click \"Add Trade-in Item\" to get started.\n                </p>\n              ) : (\n                <div className=\"space-y-4\">\n                  {tradeInItems.map((item, index) => (\n                    <div key={index} className=\"border border-purple-200 rounded-lg p-4 bg-purple-50\">\n                      <div className=\"flex justify-between items-start mb-4\">\n                        <h3 className=\"text-sm font-medium text-purple-700\">\n                          Trade-in Item #{index + 1}\n                        </h3>\n                        <button\n                          type=\"button\"\n                          onClick={() => removeTradeInItem(index)}\n                          className=\"text-red-600 hover:text-red-800\"\n                        >\n                          Remove\n                        </button>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 mb-4\">\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                            Item Name *\n                          </label>\n                          <input\n                            type=\"text\"\n                            value={item.freeformName || ''}\n                            onChange={(e) => updateTradeInItem(index, 'freeformName', e.target.value)}\n                            placeholder=\"e.g., Pikachu Base Set\"\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white\"\n                          />\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                            Condition\n                          </label>\n                          <select\n                            value={item.tradeInCondition || 'Good'}\n                            onChange={(e) => updateTradeInItem(index, 'tradeInCondition', e.target.value)}\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white\"\n                          >\n                            <option value=\"Mint\">Mint</option>\n                            <option value=\"Near Mint\">Near Mint</option>\n                            <option value=\"Excellent\">Excellent</option>\n                            <option value=\"Good\">Good</option>\n                            <option value=\"Fair\">Fair</option>\n                            <option value=\"Poor\">Poor</option>\n                          </select>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                            Trade-in Value *\n                          </label>\n                          <div className=\"relative\">\n                            <span className=\"absolute left-3 top-2 text-gray-500\">$</span>\n                            <input\n                              type=\"number\"\n                              step=\"0.01\"\n                              min=\"0\"\n                              value={item.tradeInValue || 0}\n                              onChange={(e) => updateTradeInItem(index, 'tradeInValue', parseFloat(e.target.value) || 0)}\n                              className=\"w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white\"\n                            />\n                          </div>\n                        </div>\n                        <div>\n                          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                            Notes (Optional)\n                          </label>\n                          <input\n                            type=\"text\"\n                            value={item.tradeInNotes || ''}\n                            onChange={(e) => updateTradeInItem(index, 'tradeInNotes', e.target.value)}\n                            placeholder=\"Additional notes about condition, etc.\"\n                            className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white\"\n                          />\n                        </div>\n                      </div>\n\n                      <div className=\"text-sm text-purple-700 bg-purple-100 p-2 rounded\">\n                        <strong>Trade-in Credit:</strong> ${(item.tradeInValue || 0).toFixed(2)}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Total */}\n          <div className=\"bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Transaction Summary</h2>\n\n            {transactionType === 'SALE' && tradeInItems.length > 0 ? (\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Items Subtotal:</span>\n                  <span className=\"font-medium\">\n                    ${items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0).toFixed(2)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between text-purple-600\">\n                  <span>Trade-in Credit:</span>\n                  <span className=\"font-medium\">\n                    -${tradeInItems.reduce((sum, item) => sum + (item.tradeInValue || 0), 0).toFixed(2)}\n                  </span>\n                </div>\n                <div className=\"border-t pt-2 flex justify-between items-center\">\n                  <span className=\"text-lg font-semibold text-gray-900\">Total Amount Due:</span>\n                  <span className=\"text-2xl font-bold text-gray-900\">\n                    ${calculateTotal().toFixed(2)}\n                  </span>\n                </div>\n              </div>\n            ) : (\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-lg font-semibold text-gray-900\">Total Amount:</span>\n                <span className=\"text-2xl font-bold text-gray-900\">\n                  ${calculateTotal().toFixed(2)}\n                </span>\n              </div>\n            )}\n          </div>\n\n          {/* Submit */}\n          <div className=\"flex gap-4\">\n            <Link\n              href=\"/transactions\"\n              className=\"flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg text-center hover:bg-gray-200 transition-colors\"\n            >\n              Cancel\n            </Link>\n            <button\n              type=\"submit\"\n              disabled={submitting || calculateTotal() === 0}\n              className=\"flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {submitting ? 'Creating...' : `Create ${transactionType}`}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AA6Ce,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,aAAa;IACb,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IACxF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,WAAW;IACX,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,CAAC,aAAa,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnD,MAAM;gBACN,MAAM;aACP;YAED,MAAM,eAAe,MAAM,YAAY,IAAI;YAC3C,MAAM,eAAe,MAAM,YAAY,IAAI;YAE3C,YAAY;YACZ,YAAY;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,UAAU;QACd,SAAS;eAAI;YAAO;gBAAE,WAAW;gBAAI,UAAU;gBAAG,WAAW;YAAE;SAAE;IACnE;IAEA,MAAM,kBAAkB;QACtB,SAAS;eAAI;YAAO;gBAClB,UAAU;gBACV,WAAW;gBACX,cAAc;gBACd,qBAAqB;gBACrB,aAAa;YACf;SAAE;QACF,uBAAuB;IACzB;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;eAAI;YAAc;gBAChC,UAAU;gBACV,WAAW;gBACX,WAAW;gBACX,cAAc;gBACd,kBAAkB;gBAClB,cAAc;gBACd,qBAAqB;YACvB;SAAE;IACJ;IAEA,MAAM,aAAa,CAAC;QAClB,SAAS,MAAM,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACxC;IAEA,MAAM,oBAAoB,CAAC;QACzB,gBAAgB,aAAa,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;IACtD;IAEA,MAAM,aAAa,CAAC,OAAe,OAA8B;QAC/D,MAAM,eAAe;eAAI;SAAM;QAC/B,YAAY,CAAC,MAAM,GAAG;YAAE,GAAG,YAAY,CAAC,MAAM;YAAE,CAAC,MAAM,EAAE;QAAM;QAE/D,+CAA+C;QAC/C,IAAI,UAAU,eAAe,OAAO;YAClC,MAAM,UAAU,eAAe;YAC/B,IAAI,SAAS;gBACX,YAAY,CAAC,MAAM,CAAC,SAAS,GAAG,QAAQ,SAAS;YACnD;QACF;QAEA,SAAS;IACX;IAEA,MAAM,oBAAoB,CAAC,OAAe,OAA8B;QACtE,MAAM,eAAe;eAAI;SAAa;QACtC,YAAY,CAAC,MAAM,GAAG;YAAE,GAAG,YAAY,CAAC,MAAM;YAAE,CAAC,MAAM,EAAE;QAAM;QAC/D,gBAAgB;IAClB;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrC;IAEA,MAAM,iBAAiB;QACrB,IAAI,oBAAoB,WAAW;YACjC,OAAO,WAAW,kBAAkB;QACtC;QACA,MAAM,aAAa,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,QAAQ,GAAG,KAAK,SAAS,EAAG;QACvF,MAAM,eAAe,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG;QACxF,OAAO,aAAa,aAAa,qCAAqC;;IACxE;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,cAAc;QAEd,IAAI;YACF,2CAA2C;YAC3C,MAAM,WAAW;mBACZ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,IAAI,KAAK,YAAY;mBACxD,aAAa,GAAG,CAAC,CAAA,OAAQ,CAAC;wBAAE,GAAG,IAAI;wBAAE,WAAW;oBAAK,CAAC;aAC1D;YAED,MAAM,kBAAkB;gBACtB,MAAM;gBACN;gBACA,WAAW,aAAa;gBACxB;gBACA,aAAa;gBACb,OAAO,oBAAoB,YAAY,WAAW,EAAE;YACtD;YAEA,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAgB,WAAU;sCAAsD;;;;;;sCAG3F,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAK/B,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,OAAO;4CAAQ,OAAO;4CAAQ,MAAM;4CAAM,MAAM;wCAA6B;wCAC/E;4CAAE,OAAO;4CAAY,OAAO;4CAAY,MAAM;4CAAM,MAAM;wCAA6B;wCACvF;4CAAE,OAAO;4CAAW,OAAO;4CAAW,MAAM;4CAAM,MAAM;wCAA2B;qCACpF,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC;4CAAuB,WAAU;;8DAChC,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,OAAO,KAAK,KAAK;oDACjB,SAAS,oBAAoB,KAAK,KAAK;oDACvC,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;oDAClD,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAW,CAAC,sDAAsD,EACrE,oBAAoB,KAAK,KAAK,GAC1B,+BACA,yCACJ;;sEACA,8OAAC;4DAAI,WAAU;sEAAiB,KAAK,IAAI;;;;;;sEACzC,8OAAC;4DAAI,WAAU;sEAA6B,KAAK,KAAK;;;;;;sEACtD,8OAAC;4DAAI,WAAU;sEAAyB,KAAK,IAAI;;;;;;;;;;;;;2CAhBzC,KAAK,KAAK;;;;;;;;;;;;;;;;sCAwB5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,MAAK;oDACL,QAAQ;oDACR,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,aAAa,CAAC,MAAM,EAAE,gBAAgB,WAAW,GAAG,YAAY,CAAC;oDACjE,WAAU;;;;;;;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,8OAAC;oDACC,OAAO;oDACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;gEAAwB,OAAO,QAAQ,EAAE;0EACvC,QAAQ,IAAI;+DADF,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;8CAO/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CACC,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,MAAM;4CACN,aAAY;4CACZ,WAAU;;;;;;;;;;;;;;;;;;wBAMf,oBAAoB,0BACnB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;8CACzD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAsC;;;;;;8DACtD,8OAAC;oDACC,MAAK;oDACL,MAAK;oDACL,KAAI;oDACJ,QAAQ;oDACR,OAAO;oDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,aAAY;oDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;iDAMlB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;gDAGA,oBAAoB,wBACnB,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,sBAAsB,CAAC;oDACtC,WAAU;;wDAET,qBAAqB,SAAS;wDAAM;;;;;;;;;;;;;;;;;;;gCAM5C,MAAM,MAAM,KAAK,kBAChB,8OAAC;oCAAE,WAAU;8CAAiC;;;;;yDAI9C,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;gEACX,KAAK,YAAY,GAAG,gBAAgB;gEAAe;gEAAG,QAAQ;;;;;;;sEAEjE,8OAAC;4DACC,MAAK;4DACL,SAAS,IAAM,WAAW;4DAC1B,WAAU;sEACX;;;;;;;;;;;;gDAKF,KAAK,YAAY,GAChB,0BAA0B;8DAC1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEACC,MAAK;oEACL,OAAO,KAAK,YAAY,IAAI;oEAC5B,UAAU,CAAC,IAAM,WAAW,OAAO,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEACjE,aAAY;oEACZ,WAAU;;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEACC,MAAK;oEACL,OAAO,KAAK,WAAW,IAAI;oEAC3B,UAAU,CAAC,IAAM,WAAW,OAAO,eAAe,EAAE,MAAM,CAAC,KAAK;oEAChE,aAAY;oEACZ,WAAU;;;;;;;;;;;;sEAGd,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEACC,MAAK;oEACL,OAAO,KAAK,mBAAmB,IAAI;oEACnC,UAAU,CAAC,IAAM,WAAW,OAAO,uBAAuB,EAAE,MAAM,CAAC,KAAK;oEACxE,aAAY;oEACZ,WAAU;;;;;;;;;;;;;;;;;2DAKhB,4BAA4B;8DAC5B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,8OAAC;4DACC,OAAO,KAAK,SAAS,IAAI;4DACzB,UAAU,CAAC,IAAM,WAAW,OAAO,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC9D,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;wEAAwB,OAAO,QAAQ,EAAE;;4EACvC,QAAQ,IAAI;4EAAC;4EAAG,QAAQ,QAAQ,CAAC,IAAI;4EAAC;4EAAY,QAAQ,YAAY;;uEAD5D,QAAQ,EAAE;;;;;;;;;;;;;;;;;8DAQ/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEACC,MAAK;oEACL,KAAI;oEACJ,OAAO,KAAK,QAAQ;oEACpB,UAAU,CAAC,IAAM,WAAW,OAAO,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;oEAC3E,WAAU;;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;;wEAA+C;wEAE7D,KAAK,SAAS,kBACb,8OAAC;4EAAK,WAAU;;gFAA6B;gFAC/B,eAAe,KAAK,SAAS,GAAG,UAAU,QAAQ,MAAM;gFAAO;;;;;;;;;;;;;8EAIjF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAsC;;;;;;sFACtD,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,KAAI;4EACJ,OAAO,KAAK,SAAS;4EACrB,UAAU,CAAC,IAAM,WAAW,OAAO,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EAC9E,aAAa,KAAK,SAAS,GAAG,eAAe,KAAK,SAAS,GAAG,UAAU,QAAQ,KAAK;4EACrF,WAAU;;;;;;;;;;;;;;;;;;sEAIhB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEAAI,WAAU;;wEAAmF;wEAC9F,CAAC,KAAK,QAAQ,GAAG,KAAK,SAAS,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;gDAKhD,KAAK,SAAS,IAAI,eAAe,KAAK,SAAS,mBAC9C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAO;;;;;;wDAAsB;wDAAS,eAAe,KAAK,SAAS,EAAG,YAAY;wDAAC;wDACnF,KAAK,SAAS,KAAK,eAAe,KAAK,SAAS,EAAG,SAAS,kBAC3D,8OAAC;4DAAK,WAAU;;gEAAuB;gEACZ,eAAe,KAAK,SAAS,EAAG,SAAS,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;gDAMrF,KAAK,YAAY,kBAChB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAO;;;;;;wDAAqB;wDAAE,KAAK,YAAY;wDAC/C,KAAK,mBAAmB,IAAI,CAAC,GAAG,EAAE,KAAK,mBAAmB,EAAE;;;;;;;;2CAtIzD;;;;;;;;;;;;;;;;wBAiJnB,oBAAoB,UAAU,oCAC7B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;gCAKF,aAAa,MAAM,KAAK,kBACvB,8OAAC;oCAAE,WAAU;8CAAiC;;;;;yDAI9C,8OAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,8OAAC;4CAAgB,WAAU;;8DACzB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;gEAAsC;gEAClC,QAAQ;;;;;;;sEAE1B,8OAAC;4DACC,MAAK;4DACL,SAAS,IAAM,kBAAkB;4DACjC,WAAU;sEACX;;;;;;;;;;;;8DAKH,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEACC,MAAK;oEACL,OAAO,KAAK,YAAY,IAAI;oEAC5B,UAAU,CAAC,IAAM,kBAAkB,OAAO,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEACxE,aAAY;oEACZ,WAAU;;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEACC,OAAO,KAAK,gBAAgB,IAAI;oEAChC,UAAU,CAAC,IAAM,kBAAkB,OAAO,oBAAoB,EAAE,MAAM,CAAC,KAAK;oEAC5E,WAAU;;sFAEV,8OAAC;4EAAO,OAAM;sFAAO;;;;;;sFACrB,8OAAC;4EAAO,OAAM;sFAAY;;;;;;sFAC1B,8OAAC;4EAAO,OAAM;sFAAY;;;;;;sFAC1B,8OAAC;4EAAO,OAAM;sFAAO;;;;;;sFACrB,8OAAC;4EAAO,OAAM;sFAAO;;;;;;sFACrB,8OAAC;4EAAO,OAAM;sFAAO;;;;;;;;;;;;;;;;;;sEAGzB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAsC;;;;;;sFACtD,8OAAC;4EACC,MAAK;4EACL,MAAK;4EACL,KAAI;4EACJ,OAAO,KAAK,YAAY,IAAI;4EAC5B,UAAU,CAAC,IAAM,kBAAkB,OAAO,gBAAgB,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4EACxF,WAAU;;;;;;;;;;;;;;;;;;sEAIhB,8OAAC;;8EACC,8OAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,8OAAC;oEACC,MAAK;oEACL,OAAO,KAAK,YAAY,IAAI;oEAC5B,UAAU,CAAC,IAAM,kBAAkB,OAAO,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEACxE,aAAY;oEACZ,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;sEAAO;;;;;;wDAAyB;wDAAG,CAAC,KAAK,YAAY,IAAI,CAAC,EAAE,OAAO,CAAC;;;;;;;;2CA3E/D;;;;;;;;;;;;;;;;sCAqFpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2C;;;;;;gCAExD,oBAAoB,UAAU,aAAa,MAAM,GAAG,kBACnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;;wDAAc;wDAC1B,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAO,KAAK,QAAQ,GAAG,KAAK,SAAS,EAAG,GAAG,OAAO,CAAC;;;;;;;;;;;;;sDAGrF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;oDAAK,WAAU;;wDAAc;wDACzB,aAAa,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,CAAC,KAAK,YAAY,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC;;;;;;;;;;;;;sDAGrF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAsC;;;;;;8DACtD,8OAAC;oDAAK,WAAU;;wDAAmC;wDAC/C,iBAAiB,OAAO,CAAC;;;;;;;;;;;;;;;;;;yDAKjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAsC;;;;;;sDACtD,8OAAC;4CAAK,WAAU;;gDAAmC;gDAC/C,iBAAiB,OAAO,CAAC;;;;;;;;;;;;;;;;;;;sCAOnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,UAAU,cAAc,qBAAqB;oCAC7C,WAAU;8CAET,aAAa,gBAAgB,CAAC,OAAO,EAAE,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvE", "debugId": null}}]}
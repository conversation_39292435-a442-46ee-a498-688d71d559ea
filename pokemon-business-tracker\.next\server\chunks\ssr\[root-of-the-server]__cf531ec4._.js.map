{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/SpearheadApp/pokemon-business-tracker/src/app/categories/page.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useState, useEffect } from 'react'\n\ninterface Product {\n  id: string\n  name: string\n  currentStock: number\n  isActive: boolean\n}\n\ninterface Category {\n  id: string\n  name: string\n  description?: string\n  partner1Share: number\n  partner2Share: number\n  partner3Share: number\n  partner4Share: number\n  partner5Share: number\n  isActive: boolean\n  products: Product[]\n  createdAt: string\n  updatedAt: string\n}\n\nasync function fetchCategories(): Promise<Category[]> {\n  const response = await fetch('/api/categories')\n  if (!response.ok) {\n    throw new Error('Failed to fetch categories')\n  }\n  return response.json()\n}\n\nexport default function CategoriesPage() {\n  const [categories, setCategories] = useState<Category[]>([])\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    const loadCategories = async () => {\n      try {\n        const data = await fetchCategories()\n        setCategories(data)\n      } catch (error) {\n        console.error('Error loading categories:', error)\n      } finally {\n        setLoading(false)\n      }\n    }\n\n    loadCategories()\n  }, [])\n\n  const getPartnerShareColor = (share: number) => {\n    if (share >= 30) return 'text-green-600 font-semibold'\n    if (share >= 25) return 'text-blue-600 font-medium'\n    if (share >= 20) return 'text-gray-700'\n    return 'text-gray-500'\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 p-6 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading categories...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-6\">\n      <div className=\"max-w-7xl mx-auto\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <Link href=\"/\" className=\"text-blue-600 hover:text-blue-800 mb-2 inline-block\">\n                ← Back to Dashboard\n              </Link>\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n                📂 Product Categories\n              </h1>\n              <p className=\"text-gray-600\">\n                Manage product categories and partner profit-sharing arrangements\n              </p>\n            </div>\n            <Link href=\"/categories/new\" className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n              New Category\n            </Link>\n          </div>\n        </div>\n\n        {/* Categories Grid */}\n        <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\n          {categories.length > 0 ? (\n            categories.map((category) => (\n              <div key={category.id} className=\"bg-white rounded-lg shadow hover:shadow-md transition-shadow\">\n                <div className=\"p-6\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <h3 className=\"text-lg font-semibold text-gray-900\">\n                      {category.name}\n                    </h3>\n                    <span className={`px-2 py-1 text-xs rounded-full ${\n                      category.isActive \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-gray-100 text-gray-800'\n                    }`}>\n                      {category.isActive ? 'Active' : 'Inactive'}\n                    </span>\n                  </div>\n                  \n                  {category.description && (\n                    <p className=\"text-gray-600 text-sm mb-4\">{category.description}</p>\n                  )}\n\n                  {/* Partner Profit Shares */}\n                  <div className=\"mb-4\">\n                    <h4 className=\"text-sm font-medium text-gray-700 mb-2\">Partner Profit Shares</h4>\n                    <div className=\"grid grid-cols-5 gap-1 text-xs\">\n                      <div className=\"text-center\">\n                        <div className=\"text-gray-500\">P1</div>\n                        <div className={getPartnerShareColor(category.partner1Share)}>\n                          {category.partner1Share}%\n                        </div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-gray-500\">P2</div>\n                        <div className={getPartnerShareColor(category.partner2Share)}>\n                          {category.partner2Share}%\n                        </div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-gray-500\">P3</div>\n                        <div className={getPartnerShareColor(category.partner3Share)}>\n                          {category.partner3Share}%\n                        </div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-gray-500\">P4</div>\n                        <div className={getPartnerShareColor(category.partner4Share)}>\n                          {category.partner4Share}%\n                        </div>\n                      </div>\n                      <div className=\"text-center\">\n                        <div className=\"text-gray-500\">P5</div>\n                        <div className={getPartnerShareColor(category.partner5Share)}>\n                          {category.partner5Share}%\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Products Count */}\n                  <div className=\"flex items-center justify-between text-sm text-gray-600 mb-4\">\n                    <span>{category.products.length} products</span>\n                    <span>\n                      {category.products.filter(p => p.isActive).length} active\n                    </span>\n                  </div>\n\n                  {/* Actions */}\n                  <div className=\"flex space-x-2\">\n                    <Link \n                      href={`/categories/${category.id}`}\n                      className=\"flex-1 bg-blue-600 text-white text-center py-2 px-3 rounded text-sm hover:bg-blue-700 transition-colors\"\n                    >\n                      View Details\n                    </Link>\n                    <Link \n                      href={`/categories/${category.id}/edit`}\n                      className=\"flex-1 bg-gray-100 text-gray-700 text-center py-2 px-3 rounded text-sm hover:bg-gray-200 transition-colors\"\n                    >\n                      Edit\n                    </Link>\n                  </div>\n                </div>\n              </div>\n            ))\n          ) : (\n            <div className=\"col-span-full\">\n              <div className=\"bg-white rounded-lg shadow p-12 text-center\">\n                <div className=\"text-4xl text-gray-400 mb-4\">📂</div>\n                <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No categories yet</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Create your first product category to organize your Pokemon card inventory\n                </p>\n                <Link href=\"/categories/new\" className=\"bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors\">\n                  Create First Category\n                </Link>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Summary Stats */}\n        {categories.length > 0 && (\n          <div className=\"mt-8 bg-white rounded-lg shadow p-6\">\n            <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Category Summary</h2>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-blue-600\">{categories.length}</div>\n                <div className=\"text-sm text-gray-600\">Total Categories</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-green-600\">\n                  {categories.filter(c => c.isActive).length}\n                </div>\n                <div className=\"text-sm text-gray-600\">Active Categories</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-purple-600\">\n                  {categories.reduce((sum, c) => sum + c.products.length, 0)}\n                </div>\n                <div className=\"text-sm text-gray-600\">Total Products</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-orange-600\">\n                  {categories.reduce((sum, c) => sum + c.products.filter(p => p.isActive).length, 0)}\n                </div>\n                <div className=\"text-sm text-gray-600\">Active Products</div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AA2BA,eAAe;IACb,MAAM,WAAW,MAAM,MAAM;IAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO,SAAS,IAAI;AACtB;AAEe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI;gBACF,MAAM,OAAO,MAAM;gBACnB,cAAc;YAChB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;YAC7C,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,uBAAuB,CAAC;QAC5B,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,IAAI,SAAS,IAAI,OAAO;QACxB,OAAO;IACT;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAsD;;;;;;kDAG/E,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAkB,WAAU;0CAAkF;;;;;;;;;;;;;;;;;8BAO7H,8OAAC;oBAAI,WAAU;8BACZ,WAAW,MAAM,GAAG,IACnB,WAAW,GAAG,CAAC,CAAC,yBACd,8OAAC;4BAAsB,WAAU;sCAC/B,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DACX,SAAS,IAAI;;;;;;0DAEhB,8OAAC;gDAAK,WAAW,CAAC,+BAA+B,EAC/C,SAAS,QAAQ,GACb,gCACA,6BACJ;0DACC,SAAS,QAAQ,GAAG,WAAW;;;;;;;;;;;;oCAInC,SAAS,WAAW,kBACnB,8OAAC;wCAAE,WAAU;kDAA8B,SAAS,WAAW;;;;;;kDAIjE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,8OAAC;gEAAI,WAAW,qBAAqB,SAAS,aAAa;;oEACxD,SAAS,aAAa;oEAAC;;;;;;;;;;;;;kEAG5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,8OAAC;gEAAI,WAAW,qBAAqB,SAAS,aAAa;;oEACxD,SAAS,aAAa;oEAAC;;;;;;;;;;;;;kEAG5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,8OAAC;gEAAI,WAAW,qBAAqB,SAAS,aAAa;;oEACxD,SAAS,aAAa;oEAAC;;;;;;;;;;;;;kEAG5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,8OAAC;gEAAI,WAAW,qBAAqB,SAAS,aAAa;;oEACxD,SAAS,aAAa;oEAAC;;;;;;;;;;;;;kEAG5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,8OAAC;gEAAI,WAAW,qBAAqB,SAAS,aAAa;;oEACxD,SAAS,aAAa;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;kDAOhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;oDAAM,SAAS,QAAQ,CAAC,MAAM;oDAAC;;;;;;;0DAChC,8OAAC;;oDACE,SAAS,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;oDAAC;;;;;;;;;;;;;kDAKtD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;gDAClC,WAAU;0DACX;;;;;;0DAGD,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE,CAAC,KAAK,CAAC;gDACvC,WAAU;0DACX;;;;;;;;;;;;;;;;;;2BA3EG,SAAS,EAAE;;;;kDAmFvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA8B;;;;;;8CAC7C,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAkB,WAAU;8CAAkF;;;;;;;;;;;;;;;;;;;;;;gBAShI,WAAW,MAAM,GAAG,mBACnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAoC,WAAW,MAAM;;;;;;sDACpE,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;;;;;;sDAE5C,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,CAAC,MAAM,EAAE;;;;;;sDAE1D,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,WAAW,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM,EAAE;;;;;;sDAElF,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD", "debugId": null}}]}
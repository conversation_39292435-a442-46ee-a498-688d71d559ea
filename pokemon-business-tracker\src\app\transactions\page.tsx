'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'

interface Partner {
  id: string
  name: string
}

interface Product {
  id: string
  name: string
  sku: string
}

interface TransactionItem {
  id: string
  quantity: number
  unitPrice: number
  product: Product
}

interface Transaction {
  id: string
  type: string
  description: string
  totalAmount: number
  date: string
  notes?: string
  partner?: Partner
  items: TransactionItem[]
}

type FilterType = 'ALL' | 'SALE' | 'PURCHASE' | 'EXPENSE' | 'CONTRIBUTION' | 'DISTRIBUTION'

async function fetchTransactionsData() {
  const response = await fetch('/api/transactions')
  if (!response.ok) {
    throw new Error('Failed to fetch transactions')
  }
  return response.json()
}

function getTransactionTypeColor(type: string) {
  switch (type) {
    case 'SALE': return 'text-green-600 bg-green-100'
    case 'PURCHASE': return 'text-blue-600 bg-blue-100'
    case 'EXPENSE': return 'text-red-600 bg-red-100'
    case 'CONTRIBUTION': return 'text-purple-600 bg-purple-100'
    case 'DISTRIBUTION': return 'text-orange-600 bg-orange-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

function getTransactionTypeIcon(type: string) {
  switch (type) {
    case 'SALE': return '💰'
    case 'PURCHASE': return '🛒'
    case 'EXPENSE': return '💸'
    case 'CONTRIBUTION': return '💵'
    case 'DISTRIBUTION': return '🎁'
    default: return '📄'
  }
}

export default function TransactionsPage() {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [activeFilter, setActiveFilter] = useState<FilterType>('ALL')

  useEffect(() => {
    const loadTransactions = async () => {
      try {
        const data = await fetchTransactionsData()
        setTransactions(data)
      } catch (error) {
        console.error('Error loading transactions:', error)
      } finally {
        setLoading(false)
      }
    }

    loadTransactions()
  }, [])

  // Filter transactions based on active filter
  const filteredTransactions = transactions.filter(transaction => {
    if (activeFilter === 'ALL') return true
    return transaction.type === activeFilter
  })

  // Calculate summary statistics
  const totalSales = transactions
    .filter(t => t.type === 'SALE')
    .reduce((sum, t) => sum + t.totalAmount, 0)

  const totalExpenses = transactions
    .filter(t => t.type === 'EXPENSE')
    .reduce((sum, t) => sum + t.totalAmount, 0)

  const totalPurchases = transactions
    .filter(t => t.type === 'PURCHASE')
    .reduce((sum, t) => sum + t.totalAmount, 0)

  const netProfit = totalSales - totalExpenses - totalPurchases

  const summary = {
    totalSales,
    totalExpenses,
    totalPurchases,
    netProfit,
    totalTransactions: transactions.length
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading transactions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <Link href="/" className="text-blue-600 hover:text-blue-800 mb-2 inline-block">
                ← Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                💳 Transactions
              </h1>
              <p className="text-gray-600">
                Track all business transactions with cash accounting
              </p>
            </div>
            <Link href="/transactions/new" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              New Transaction
            </Link>
          </div>
        </div>

        {/* Financial Summary */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <span className="text-2xl">💰</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Sales</p>
                <p className="text-2xl font-semibold text-green-600">
                  ${summary.totalSales.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-2xl">🛒</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Purchases</p>
                <p className="text-2xl font-semibold text-blue-600">
                  ${summary.totalPurchases.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <span className="text-2xl">💸</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Expenses</p>
                <p className="text-2xl font-semibold text-red-600">
                  ${summary.totalExpenses.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`p-2 rounded-lg ${summary.netProfit >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                <span className="text-2xl">{summary.netProfit >= 0 ? '📈' : '📉'}</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Net Profit</p>
                <p className={`text-2xl font-semibold ${summary.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${summary.netProfit.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-gray-100 rounded-lg">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Transactions</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {summary.totalTransactions}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Transaction Filters */}
        <div className="bg-white rounded-lg shadow mb-6 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Filter Transactions</h2>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => setActiveFilter('ALL')}
              className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                activeFilter === 'ALL'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              All ({transactions.length})
            </button>
            <button
              onClick={() => setActiveFilter('SALE')}
              className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                activeFilter === 'SALE'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Sales ({transactions.filter(t => t.type === 'SALE').length})
            </button>
            <button
              onClick={() => setActiveFilter('PURCHASE')}
              className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                activeFilter === 'PURCHASE'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Purchases ({transactions.filter(t => t.type === 'PURCHASE').length})
            </button>
            <button
              onClick={() => setActiveFilter('EXPENSE')}
              className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                activeFilter === 'EXPENSE'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Expenses ({transactions.filter(t => t.type === 'EXPENSE').length})
            </button>
            <button
              onClick={() => setActiveFilter('CONTRIBUTION')}
              className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                activeFilter === 'CONTRIBUTION'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Contributions ({transactions.filter(t => t.type === 'CONTRIBUTION').length})
            </button>
            <button
              onClick={() => setActiveFilter('DISTRIBUTION')}
              className={`px-4 py-2 rounded-lg text-sm transition-colors ${
                activeFilter === 'DISTRIBUTION'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Distributions ({transactions.filter(t => t.type === 'DISTRIBUTION').length})
            </button>
          </div>
        </div>

        {/* Transactions List */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Transaction History</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {filteredTransactions.length > 0 ? (
              filteredTransactions.map((transaction) => (
                <div key={transaction.id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl">
                        {getTransactionTypeIcon(transaction.type)}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="text-lg font-medium text-gray-900">
                            {transaction.description}
                          </h3>
                          <span className={`px-2 py-1 text-xs rounded-full ${getTransactionTypeColor(transaction.type)}`}>
                            {transaction.type}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>
                            {new Date(transaction.date).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                          {transaction.partner && (
                            <span>• {transaction.partner.name}</span>
                          )}
                          {transaction.items.length > 0 && (
                            <span>• {transaction.items.length} item(s)</span>
                          )}
                        </div>
                        {transaction.notes && (
                          <p className="text-sm text-gray-600 mt-1">{transaction.notes}</p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-xl font-semibold ${
                        transaction.type === 'SALE' || transaction.type === 'CONTRIBUTION' 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {transaction.type === 'SALE' || transaction.type === 'CONTRIBUTION' ? '+' : '-'}
                        ${transaction.totalAmount.toFixed(2)}
                      </div>
                      <div className="text-sm text-gray-600">
                        Cash {transaction.cashIn > 0 ? 'In' : 'Out'}
                      </div>
                    </div>
                  </div>

                  {/* Transaction Items */}
                  {transaction.items.length > 0 && (
                    <div className="mt-4 pl-12">
                      <div className="bg-gray-50 rounded-lg p-3">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Items:</h4>
                        <div className="space-y-1">
                          {transaction.items.map((item) => (
                            <div key={item.id} className="flex justify-between text-sm">
                              <span className="text-gray-600">
                                {item.product.name} × {item.quantity}
                              </span>
                              <span className="font-medium">
                                ${(item.quantity * item.unitPrice).toFixed(2)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="p-12 text-center">
                <div className="text-4xl text-gray-400 mb-4">📄</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {activeFilter === 'ALL' ? 'No transactions yet' : `No ${activeFilter.toLowerCase()} transactions`}
                </h3>
                <p className="text-gray-600 mb-4">
                  {activeFilter === 'ALL'
                    ? 'Start by recording your first sale, purchase, or expense'
                    : `No ${activeFilter.toLowerCase()} transactions found. Try a different filter or create a new transaction.`
                  }
                </p>
                <Link href="/transactions/new" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  {activeFilter === 'ALL' ? 'Add First Transaction' : 'Create Transaction'}
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'

interface Partner {
  id: string
  name: string
}

interface Product {
  id: string
  name: string
  sku: string
}

interface TransactionItem {
  id: string
  quantity: number
  unitPrice: number
  product: Product
}

interface Transaction {
  id: string
  type: string
  description: string
  totalAmount: number
  date: string
  notes?: string
  partner?: Partner
  items: TransactionItem[]
}

type FilterType = 'ALL' | 'SALE' | 'PURCHASE' | 'EXPENSE' | 'CONTRIBUTION' | 'DISTRIBUTION'
type DateRangeType = 'ALL' | 'TODAY' | 'WEEK' | 'MONTH' | 'CUSTOM'
type SortField = 'date' | 'amount' | 'type' | 'description'
type SortOrder = 'asc' | 'desc'

async function fetchTransactionsData() {
  const response = await fetch('/api/transactions')
  if (!response.ok) {
    throw new Error('Failed to fetch transactions')
  }
  return response.json()
}

// Helper functions for date filtering
function getDateRangeFilter(range: DateRangeType, customStart?: Date, customEnd?: Date) {
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

  switch (range) {
    case 'TODAY':
      return {
        start: today,
        end: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
      }
    case 'WEEK':
      const weekStart = new Date(today)
      weekStart.setDate(today.getDate() - today.getDay())
      const weekEnd = new Date(weekStart)
      weekEnd.setDate(weekStart.getDate() + 6)
      weekEnd.setHours(23, 59, 59, 999)
      return { start: weekStart, end: weekEnd }
    case 'MONTH':
      const monthStart = new Date(today.getFullYear(), today.getMonth(), 1)
      const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0)
      monthEnd.setHours(23, 59, 59, 999)
      return { start: monthStart, end: monthEnd }
    case 'CUSTOM':
      return {
        start: customStart || new Date(0),
        end: customEnd || new Date()
      }
    default:
      return null
  }
}

function getTransactionTypeColor(type: string) {
  switch (type) {
    case 'SALE': return 'text-green-600 bg-green-100'
    case 'PURCHASE': return 'text-blue-600 bg-blue-100'
    case 'EXPENSE': return 'text-red-600 bg-red-100'
    case 'CONTRIBUTION': return 'text-purple-600 bg-purple-100'
    case 'DISTRIBUTION': return 'text-orange-600 bg-orange-100'
    default: return 'text-gray-600 bg-gray-100'
  }
}

function getTransactionTypeIcon(type: string) {
  switch (type) {
    case 'SALE': return '💰'
    case 'PURCHASE': return '🛒'
    case 'EXPENSE': return '💸'
    case 'CONTRIBUTION': return '💵'
    case 'DISTRIBUTION': return '🎁'
    default: return '📄'
  }
}

export default function TransactionsPage() {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(true)
  const [activeFilter, setActiveFilter] = useState<FilterType>('ALL')
  const [dateRange, setDateRange] = useState<DateRangeType>('ALL')
  const [customStartDate, setCustomStartDate] = useState('')
  const [customEndDate, setCustomEndDate] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [sortField, setSortField] = useState<SortField>('date')
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(20)

  useEffect(() => {
    const loadTransactions = async () => {
      try {
        const data = await fetchTransactionsData()
        setTransactions(data)
      } catch (error) {
        console.error('Error loading transactions:', error)
      } finally {
        setLoading(false)
      }
    }

    loadTransactions()
  }, [])

  // Filter and sort transactions
  const filteredAndSortedTransactions = transactions
    .filter(transaction => {
      // Type filter
      if (activeFilter !== 'ALL' && transaction.type !== activeFilter) {
        return false
      }

      // Date range filter
      const dateFilter = getDateRangeFilter(
        dateRange,
        customStartDate ? new Date(customStartDate) : undefined,
        customEndDate ? new Date(customEndDate) : undefined
      )
      if (dateFilter) {
        const transactionDate = new Date(transaction.date)
        if (transactionDate < dateFilter.start || transactionDate > dateFilter.end) {
          return false
        }
      }

      // Search filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase()
        const matchesDescription = transaction.description.toLowerCase().includes(query)
        const matchesPartner = transaction.partner?.name.toLowerCase().includes(query) || false
        const matchesNotes = transaction.notes?.toLowerCase().includes(query) || false
        const matchesAmount = transaction.totalAmount.toString().includes(query)

        if (!matchesDescription && !matchesPartner && !matchesNotes && !matchesAmount) {
          return false
        }
      }

      return true
    })
    .sort((a, b) => {
      let aValue: any, bValue: any

      switch (sortField) {
        case 'date':
          aValue = new Date(a.date).getTime()
          bValue = new Date(b.date).getTime()
          break
        case 'amount':
          aValue = a.totalAmount
          bValue = b.totalAmount
          break
        case 'type':
          aValue = a.type
          bValue = b.type
          break
        case 'description':
          aValue = a.description.toLowerCase()
          bValue = b.description.toLowerCase()
          break
        default:
          return 0
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
      }
    })

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedTransactions.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const paginatedTransactions = filteredAndSortedTransactions.slice(startIndex, endIndex)

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [activeFilter, dateRange, searchQuery, sortField, sortOrder])

  // Export to CSV function
  const exportToCSV = () => {
    const headers = [
      'Date',
      'Type',
      'Description',
      'Partner',
      'Amount',
      'Notes',
      'Items'
    ]

    const csvData = filteredAndSortedTransactions.map(transaction => [
      new Date(transaction.date).toLocaleDateString('en-US'),
      transaction.type,
      transaction.description,
      transaction.partner?.name || '',
      transaction.totalAmount.toFixed(2),
      transaction.notes || '',
      transaction.items.map(item =>
        `${item.product.name} (${item.quantity} × $${item.unitPrice.toFixed(2)})`
      ).join('; ')
    ])

    const csvContent = [
      headers.join(','),
      ...csvData.map(row =>
        row.map(cell =>
          typeof cell === 'string' && (cell.includes(',') || cell.includes('"'))
            ? `"${cell.replace(/"/g, '""')}"`
            : cell
        ).join(',')
      )
    ].join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `transactions_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  // Calculate summary statistics
  const totalSales = transactions
    .filter(t => t.type === 'SALE')
    .reduce((sum, t) => sum + t.totalAmount, 0)

  const totalExpenses = transactions
    .filter(t => t.type === 'EXPENSE')
    .reduce((sum, t) => sum + t.totalAmount, 0)

  const totalPurchases = transactions
    .filter(t => t.type === 'PURCHASE')
    .reduce((sum, t) => sum + t.totalAmount, 0)

  const netProfit = totalSales - totalExpenses - totalPurchases

  const summary = {
    totalSales,
    totalExpenses,
    totalPurchases,
    netProfit,
    totalTransactions: transactions.length
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading transactions...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <Link href="/" className="text-blue-600 hover:text-blue-800 mb-2 inline-block">
                ← Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                💳 Transactions
              </h1>
              <p className="text-gray-600">
                Track all business transactions with cash accounting
              </p>
            </div>
            <Link href="/transactions/new" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              New Transaction
            </Link>
          </div>
        </div>

        {/* Financial Summary */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <span className="text-2xl">💰</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Sales</p>
                <p className="text-2xl font-semibold text-green-600">
                  ${summary.totalSales.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-2xl">🛒</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Purchases</p>
                <p className="text-2xl font-semibold text-blue-600">
                  ${summary.totalPurchases.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <span className="text-2xl">💸</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Expenses</p>
                <p className="text-2xl font-semibold text-red-600">
                  ${summary.totalExpenses.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className={`p-2 rounded-lg ${summary.netProfit >= 0 ? 'bg-green-100' : 'bg-red-100'}`}>
                <span className="text-2xl">{summary.netProfit >= 0 ? '📈' : '📉'}</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Net Profit</p>
                <p className={`text-2xl font-semibold ${summary.netProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  ${summary.netProfit.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-gray-100 rounded-lg">
                <span className="text-2xl">📊</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Transactions</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {summary.totalTransactions}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Advanced Filters */}
        <div className="bg-white rounded-lg shadow mb-6 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Filter & Search Transactions</h2>

          {/* Search Bar */}
          <div className="mb-6">
            <div className="relative">
              <input
                type="text"
                placeholder="Search by description, partner, notes, or amount..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          {/* Type Filters */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Transaction Type</h3>
            <div className="flex flex-wrap gap-2">
              {(['ALL', 'SALE', 'PURCHASE', 'EXPENSE', 'CONTRIBUTION', 'DISTRIBUTION'] as FilterType[]).map((type) => (
                <button
                  key={type}
                  onClick={() => setActiveFilter(type)}
                  className={`px-3 py-1.5 rounded-lg text-sm transition-colors ${
                    activeFilter === type
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {type === 'ALL' ? 'All' : type.charAt(0) + type.slice(1).toLowerCase()}s ({
                    type === 'ALL'
                      ? transactions.length
                      : transactions.filter(t => t.type === type).length
                  })
                </button>
              ))}
            </div>
          </div>

          {/* Date Range Filters */}
          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-700 mb-3">Date Range</h3>
            <div className="flex flex-wrap gap-2 mb-3">
              {(['ALL', 'TODAY', 'WEEK', 'MONTH', 'CUSTOM'] as DateRangeType[]).map((range) => (
                <button
                  key={range}
                  onClick={() => setDateRange(range)}
                  className={`px-3 py-1.5 rounded-lg text-sm transition-colors ${
                    dateRange === range
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {range === 'ALL' ? 'All Time' :
                   range === 'TODAY' ? 'Today' :
                   range === 'WEEK' ? 'This Week' :
                   range === 'MONTH' ? 'This Month' : 'Custom Range'}
                </button>
              ))}
            </div>

            {/* Custom Date Range Inputs */}
            {dateRange === 'CUSTOM' && (
              <div className="flex gap-3 items-center">
                <div>
                  <label className="block text-xs text-gray-600 mb-1">From</label>
                  <input
                    type="date"
                    value={customStartDate}
                    onChange={(e) => setCustomStartDate(e.target.value)}
                    className="px-3 py-1.5 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-600 mb-1">To</label>
                  <input
                    type="date"
                    value={customEndDate}
                    onChange={(e) => setCustomEndDate(e.target.value)}
                    className="px-3 py-1.5 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            )}
          </div>

          {/* Sort Options */}
          <div className="flex flex-wrap gap-4 items-center">
            <div>
              <label className="block text-xs text-gray-600 mb-1">Sort by</label>
              <select
                value={sortField}
                onChange={(e) => setSortField(e.target.value as SortField)}
                className="px-3 py-1.5 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="date">Date</option>
                <option value="amount">Amount</option>
                <option value="type">Type</option>
                <option value="description">Description</option>
              </select>
            </div>
            <div>
              <label className="block text-xs text-gray-600 mb-1">Order</label>
              <select
                value={sortOrder}
                onChange={(e) => setSortOrder(e.target.value as SortOrder)}
                className="px-3 py-1.5 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="desc">Newest First</option>
                <option value="asc">Oldest First</option>
              </select>
            </div>
            <div className="ml-auto">
              <div className="text-sm text-gray-600">
                Showing {filteredAndSortedTransactions.length} of {transactions.length} transactions
              </div>
            </div>
          </div>
        </div>

        {/* Transactions List */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Transaction History</h2>
          </div>
          <div className="divide-y divide-gray-200">
            {paginatedTransactions.length > 0 ? (
              paginatedTransactions.map((transaction) => (
                <div key={transaction.id} className="p-6 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="text-2xl">
                        {getTransactionTypeIcon(transaction.type)}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className="text-lg font-medium text-gray-900">
                            {transaction.description}
                          </h3>
                          <span className={`px-2 py-1 text-xs rounded-full ${getTransactionTypeColor(transaction.type)}`}>
                            {transaction.type}
                          </span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span>
                            {new Date(transaction.date).toLocaleDateString('en-US', {
                              year: 'numeric',
                              month: 'short',
                              day: 'numeric',
                              hour: '2-digit',
                              minute: '2-digit'
                            })}
                          </span>
                          {transaction.partner && (
                            <span>• {transaction.partner.name}</span>
                          )}
                          {transaction.items.length > 0 && (
                            <span>• {transaction.items.length} item(s)</span>
                          )}
                        </div>
                        {transaction.notes && (
                          <p className="text-sm text-gray-600 mt-1">{transaction.notes}</p>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-xl font-semibold ${
                        transaction.type === 'SALE' || transaction.type === 'CONTRIBUTION' 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {transaction.type === 'SALE' || transaction.type === 'CONTRIBUTION' ? '+' : '-'}
                        ${transaction.totalAmount.toFixed(2)}
                      </div>
                      <div className="text-sm text-gray-600">
                        Cash {transaction.cashIn > 0 ? 'In' : 'Out'}
                      </div>
                    </div>
                  </div>

                  {/* Transaction Items */}
                  {transaction.items.length > 0 && (
                    <div className="mt-4 pl-12">
                      <div className="bg-gray-50 rounded-lg p-3">
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Items:</h4>
                        <div className="space-y-1">
                          {transaction.items.map((item) => (
                            <div key={item.id} className="flex justify-between text-sm">
                              <span className="text-gray-600">
                                {item.product.name} × {item.quantity}
                              </span>
                              <span className="font-medium">
                                ${(item.quantity * item.unitPrice).toFixed(2)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="p-12 text-center">
                <div className="text-4xl text-gray-400 mb-4">📄</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {filteredAndSortedTransactions.length === 0 && transactions.length === 0
                    ? 'No transactions yet'
                    : 'No transactions match your filters'
                  }
                </h3>
                <p className="text-gray-600 mb-4">
                  {filteredAndSortedTransactions.length === 0 && transactions.length === 0
                    ? 'Start by recording your first sale, purchase, or expense'
                    : 'Try adjusting your filters or search terms to find transactions.'
                  }
                </p>
                <Link href="/transactions/new" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Create Transaction
                </Link>
              </div>
            )}
          </div>

          {/* Pagination */}
          {filteredAndSortedTransactions.length > itemsPerPage && (
            <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div className="text-sm text-gray-600">
                Showing {startIndex + 1} to {Math.min(endIndex, filteredAndSortedTransactions.length)} of {filteredAndSortedTransactions.length} results
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>

                {/* Page Numbers */}
                <div className="flex space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum
                    if (totalPages <= 5) {
                      pageNum = i + 1
                    } else if (currentPage <= 3) {
                      pageNum = i + 1
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i
                    } else {
                      pageNum = currentPage - 2 + i
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-1 text-sm border rounded ${
                          currentPage === pageNum
                            ? 'bg-blue-600 text-white border-blue-600'
                            : 'border-gray-300 hover:bg-gray-50'
                        }`}
                      >
                        {pageNum}
                      </button>
                    )
                  })}
                </div>

                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Export Button */}
        {filteredAndSortedTransactions.length > 0 && (
          <div className="mt-6 text-center">
            <button
              onClick={exportToCSV}
              className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              📊 Export to CSV ({filteredAndSortedTransactions.length} transactions)
            </button>
          </div>
        )}
      </div>
    </div>
  )
}

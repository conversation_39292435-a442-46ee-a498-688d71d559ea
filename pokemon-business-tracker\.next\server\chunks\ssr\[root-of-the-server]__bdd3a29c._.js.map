{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/SpearheadApp/pokemon-business-tracker/src/app/categories/new/page.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useState } from 'react'\nimport { useRouter } from 'next/navigation'\n\nexport default function NewCategoryPage() {\n  const router = useRouter()\n  const [submitting, setSubmitting] = useState(false)\n\n  // Form state\n  const [name, setName] = useState('')\n  const [description, setDescription] = useState('')\n  const [partner1Share, setPartner1Share] = useState(20)\n  const [partner2Share, setPartner2Share] = useState(20)\n  const [partner3Share, setPartner3Share] = useState(20)\n  const [partner4Share, setPartner4Share] = useState(20)\n  const [partner5Share, setPartner5Share] = useState(20)\n  const [isActive, setIsActive] = useState(true)\n\n  const totalShare = partner1Share + partner2Share + partner3Share + partner4Share + partner5Share\n  const isValidShare = Math.abs(totalShare - 100) < 0.01\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    \n    if (!isValidShare) {\n      alert('Partner profit shares must add up to 100%')\n      return\n    }\n\n    setSubmitting(true)\n\n    try {\n      const categoryData = {\n        name,\n        description,\n        partner1Share,\n        partner2Share,\n        partner3Share,\n        partner4Share,\n        partner5Share,\n        isActive\n      }\n\n      const response = await fetch('/api/categories', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(categoryData),\n      })\n\n      if (response.ok) {\n        router.push('/categories')\n      } else {\n        const error = await response.json()\n        throw new Error(error.error || 'Failed to create category')\n      }\n    } catch (error) {\n      console.error('Error creating category:', error)\n      alert(`Error creating category: ${error instanceof Error ? error.message : 'Please try again.'}`)\n    } finally {\n      setSubmitting(false)\n    }\n  }\n\n  const handleShareChange = (partner: number, value: number) => {\n    switch (partner) {\n      case 1: setPartner1Share(value); break\n      case 2: setPartner2Share(value); break\n      case 3: setPartner3Share(value); break\n      case 4: setPartner4Share(value); break\n      case 5: setPartner5Share(value); break\n    }\n  }\n\n  const distributeEqually = () => {\n    setPartner1Share(20)\n    setPartner2Share(20)\n    setPartner3Share(20)\n    setPartner4Share(20)\n    setPartner5Share(20)\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 p-6\">\n      <div className=\"max-w-2xl mx-auto\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <Link href=\"/categories\" className=\"text-blue-600 hover:text-blue-800 mb-2 inline-block\">\n            ← Back to Categories\n          </Link>\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            📂 New Product Category\n          </h1>\n          <p className=\"text-gray-600\">\n            Create a new product category with partner profit-sharing arrangement\n          </p>\n        </div>\n\n        {/* Form */}\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {/* Basic Information */}\n            <div>\n              <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Basic Information</h2>\n              \n              <div className=\"space-y-4\">\n                <div>\n                  <label htmlFor=\"name\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Category Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    id=\"name\"\n                    value={name}\n                    onChange={(e) => setName(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"e.g., Pokemon Cards, Booster Packs, Accessories\"\n                    required\n                  />\n                </div>\n\n                <div>\n                  <label htmlFor=\"description\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Description\n                  </label>\n                  <textarea\n                    id=\"description\"\n                    value={description}\n                    onChange={(e) => setDescription(e.target.value)}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                    placeholder=\"Brief description of this product category\"\n                  />\n                </div>\n\n                <div className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    id=\"isActive\"\n                    checked={isActive}\n                    onChange={(e) => setIsActive(e.target.checked)}\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <label htmlFor=\"isActive\" className=\"ml-2 block text-sm text-gray-700\">\n                    Active category\n                  </label>\n                </div>\n              </div>\n            </div>\n\n            {/* Partner Profit Sharing */}\n            <div>\n              <div className=\"flex items-center justify-between mb-4\">\n                <h2 className=\"text-lg font-semibold text-gray-900\">Partner Profit Sharing</h2>\n                <button\n                  type=\"button\"\n                  onClick={distributeEqually}\n                  className=\"text-sm text-blue-600 hover:text-blue-800\"\n                >\n                  Distribute Equally\n                </button>\n              </div>\n              \n              <div className=\"space-y-4\">\n                {[1, 2, 3, 4, 5].map((partnerNum) => (\n                  <div key={partnerNum} className=\"flex items-center space-x-4\">\n                    <label className=\"w-20 text-sm font-medium text-gray-700\">\n                      Partner {partnerNum}\n                    </label>\n                    <div className=\"flex-1\">\n                      <input\n                        type=\"number\"\n                        min=\"0\"\n                        max=\"100\"\n                        step=\"0.1\"\n                        value={\n                          partnerNum === 1 ? partner1Share :\n                          partnerNum === 2 ? partner2Share :\n                          partnerNum === 3 ? partner3Share :\n                          partnerNum === 4 ? partner4Share :\n                          partner5Share\n                        }\n                        onChange={(e) => handleShareChange(partnerNum, parseFloat(e.target.value) || 0)}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      />\n                    </div>\n                    <span className=\"w-8 text-sm text-gray-500\">%</span>\n                  </div>\n                ))}\n              </div>\n\n              {/* Total Share Validation */}\n              <div className=\"mt-4 p-3 rounded-lg bg-gray-50\">\n                <div className=\"flex items-center justify-between\">\n                  <span className=\"text-sm font-medium text-gray-700\">Total Share:</span>\n                  <span className={`text-sm font-semibold ${\n                    isValidShare ? 'text-green-600' : 'text-red-600'\n                  }`}>\n                    {totalShare.toFixed(1)}%\n                  </span>\n                </div>\n                {!isValidShare && (\n                  <p className=\"text-xs text-red-600 mt-1\">\n                    Partner shares must add up to exactly 100%\n                  </p>\n                )}\n              </div>\n            </div>\n\n            {/* Submit Button */}\n            <div className=\"flex space-x-4\">\n              <button\n                type=\"submit\"\n                disabled={submitting || !isValidShare || !name.trim()}\n                className=\"flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n              >\n                {submitting ? 'Creating...' : 'Create Category'}\n              </button>\n              <Link\n                href=\"/categories\"\n                className=\"flex-1 bg-gray-100 text-gray-700 text-center py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors\"\n              >\n                Cancel\n              </Link>\n            </div>\n          </form>\n        </div>\n\n        {/* Help Text */}\n        <div className=\"mt-6 bg-blue-50 rounded-lg p-4\">\n          <h3 className=\"text-sm font-medium text-blue-900 mb-2\">💡 Profit Sharing Tips</h3>\n          <ul className=\"text-sm text-blue-800 space-y-1\">\n            <li>• Different categories can have different profit-sharing arrangements</li>\n            <li>• Consider expertise: Give higher shares to partners who specialize in certain categories</li>\n            <li>• All partner shares must add up to exactly 100%</li>\n            <li>• You can change these percentages later by editing the category</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,aAAa;IACb,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,aAAa,gBAAgB,gBAAgB,gBAAgB,gBAAgB;IACnF,MAAM,eAAe,KAAK,GAAG,CAAC,aAAa,OAAO;IAElD,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,cAAc;YACjB,MAAM;YACN;QACF;QAEA,cAAc;QAEd,IAAI;YACF,MAAM,eAAe;gBACnB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;YAEA,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,MAAM,QAAQ,MAAM,SAAS,IAAI;gBACjC,MAAM,IAAI,MAAM,MAAM,KAAK,IAAI;YACjC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM,CAAC,yBAAyB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,qBAAqB;QAClG,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,oBAAoB,CAAC,SAAiB;QAC1C,OAAQ;YACN,KAAK;gBAAG,iBAAiB;gBAAQ;YACjC,KAAK;gBAAG,iBAAiB;gBAAQ;YACjC,KAAK;gBAAG,iBAAiB;gBAAQ;YACjC,KAAK;gBAAG,iBAAiB;gBAAQ;YACjC,KAAK;gBAAG,iBAAiB;gBAAQ;QACnC;IACF;IAEA,MAAM,oBAAoB;QACxB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;IACnB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAc,WAAU;sCAAsD;;;;;;sCAGzF,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAM/B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAK,UAAU;wBAAc,WAAU;;0CAEtC,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2C;;;;;;kDAEzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAO,WAAU;kEAA+C;;;;;;kEAG/E,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;wDACvC,WAAU;wDACV,aAAY;wDACZ,QAAQ;;;;;;;;;;;;0DAIZ,8OAAC;;kEACC,8OAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAA+C;;;;;;kEAGtF,8OAAC;wDACC,IAAG;wDACH,OAAO;wDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC9C,MAAM;wDACN,WAAU;wDACV,aAAY;;;;;;;;;;;;0DAIhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS;wDACT,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,OAAO;wDAC7C,WAAU;;;;;;kEAEZ,8OAAC;wDAAM,SAAQ;wDAAW,WAAU;kEAAmC;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7E,8OAAC;;kDACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAsC;;;;;;0DACpD,8OAAC;gDACC,MAAK;gDACL,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAKH,8OAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAG;4CAAG;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,2BACpB,8OAAC;gDAAqB,WAAU;;kEAC9B,8OAAC;wDAAM,WAAU;;4DAAyC;4DAC/C;;;;;;;kEAEX,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,MAAK;4DACL,KAAI;4DACJ,KAAI;4DACJ,MAAK;4DACL,OACE,eAAe,IAAI,gBACnB,eAAe,IAAI,gBACnB,eAAe,IAAI,gBACnB,eAAe,IAAI,gBACnB;4DAEF,UAAU,CAAC,IAAM,kBAAkB,YAAY,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4DAC7E,WAAU;;;;;;;;;;;kEAGd,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;+CArBpC;;;;;;;;;;kDA2Bd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAoC;;;;;;kEACpD,8OAAC;wDAAK,WAAW,CAAC,sBAAsB,EACtC,eAAe,mBAAmB,gBAClC;;4DACC,WAAW,OAAO,CAAC;4DAAG;;;;;;;;;;;;;4CAG1B,CAAC,8BACA,8OAAC;gDAAE,WAAU;0DAA4B;;;;;;;;;;;;;;;;;;0CAQ/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,UAAU,cAAc,CAAC,gBAAgB,CAAC,KAAK,IAAI;wCACnD,WAAU;kDAET,aAAa,gBAAgB;;;;;;kDAEhC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB", "debugId": null}}]}
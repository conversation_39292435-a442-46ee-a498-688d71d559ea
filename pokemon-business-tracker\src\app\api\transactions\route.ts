import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const transactions = await prisma.transaction.findMany({
      include: {
        partner: {
          select: {
            id: true,
            name: true
          }
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                sku: true
              }
            }
          }
        }
      },
      orderBy: { date: 'desc' }
    })

    return NextResponse.json(transactions)
  } catch (error) {
    console.error('Error fetching transactions:', error)
    return NextResponse.json(
      { error: 'Failed to fetch transactions' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { type, description, partnerId, notes, totalAmount, items } = body

    // Validate required fields
    if (!type || !description || totalAmount <= 0) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Start a transaction to ensure data consistency
    const result = await prisma.$transaction(async (tx) => {
      // Create the main transaction
      const transaction = await tx.transaction.create({
        data: {
          type,
          description,
          totalAmount,
          partnerId: partnerId || null,
          notes: notes || null,
          cashIn: type === 'SALE' || type === 'CONTRIBUTION' ? totalAmount : 0,
          cashOut: type === 'PURCHASE' || type === 'EXPENSE' || type === 'DISTRIBUTION' ? totalAmount : 0,
        }
      })

      // Create transaction items if any
      if (items && items.length > 0) {
        for (const item of items) {
          // Validate item data
          if (item.quantity <= 0 || item.unitPrice < 0) {
            throw new Error('Invalid item data: quantity and price must be positive')
          }

          // For regular products, productId is required
          // For freeform products, freeformName is required
          // For trade-ins, isTradeIn should be true
          if (!item.productId && !item.freeformName && !item.isTradeIn) {
            throw new Error('Item must have either productId or freeformName')
          }

          // Create transaction item
          await tx.transactionItem.create({
            data: {
              transactionId: transaction.id,
              productId: item.productId || null,
              quantity: item.quantity,
              unitPrice: item.unitPrice,
              totalPrice: item.quantity * item.unitPrice,

              // Freeform product fields
              freeformName: item.freeformName || null,
              freeformDescription: item.freeformDescription || null,
              freeformSku: item.freeformSku || null,

              // Trade-in fields
              isTradeIn: item.isTradeIn || false,
              tradeInValue: item.tradeInValue || null,
              tradeInCondition: item.tradeInCondition || null,
              tradeInNotes: item.tradeInNotes || null
            }
          })

          // Update product stock only for regular products (not freeform or trade-ins)
          if (item.productId && !item.isTradeIn) {
            if (type === 'SALE') {
              // Decrease stock for sales
              await tx.product.update({
                where: { id: item.productId },
                data: {
                  currentStock: {
                    decrement: item.quantity
                  }
                }
              })

              // Log inventory change
              await tx.inventoryLog.create({
                data: {
                  productId: item.productId,
                changeType: 'OUT',
                quantity: -item.quantity,
                reason: 'SALE',
                notes: `Sale transaction: ${transaction.id}`
              }
            })
          } else if (type === 'PURCHASE') {
            // Increase stock for purchases
            await tx.product.update({
              where: { id: item.productId },
              data: {
                currentStock: {
                  increment: item.quantity
                }
              }
            })

            // Log inventory change
            await tx.inventoryLog.create({
              data: {
                productId: item.productId,
                changeType: 'IN',
                quantity: item.quantity,
                reason: 'PURCHASE',
                notes: `Purchase transaction: ${transaction.id}`
              }
            })
          }
        }
      }

      return transaction
    })

    return NextResponse.json(result, { status: 201 })
  } catch (error) {
    console.error('Error creating transaction:', error)
    return NextResponse.json(
      { error: 'Failed to create transaction' },
      { status: 500 }
    )
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET() {
  try {
    const categories = await prisma.productCategory.findMany({
      include: {
        products: {
          select: {
            id: true,
            name: true,
            currentStock: true,
            isActive: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    return NextResponse.json(categories)
  } catch (error) {
    console.error('Error fetching categories:', error)
    return NextResponse.json(
      { error: 'Failed to fetch categories' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      name,
      description,
      partner1Share,
      partner2Share,
      partner3Share,
      partner4Share,
      partner5Share,
      isActive
    } = body

    // Validate required fields
    if (!name) {
      return NextResponse.json(
        { error: 'Category name is required' },
        { status: 400 }
      )
    }

    // Validate profit shares add up to 100%
    const totalShare = (partner1Share || 20) + (partner2Share || 20) + (partner3Share || 20) + (partner4Share || 20) + (partner5Share || 20)
    if (Math.abs(totalShare - 100) > 0.01) {
      return NextResponse.json(
        { error: 'Partner profit shares must add up to 100%' },
        { status: 400 }
      )
    }

    // Check if category name already exists
    const existingCategory = await prisma.productCategory.findUnique({
      where: { name }
    })
    if (existingCategory) {
      return NextResponse.json(
        { error: 'Category name already exists' },
        { status: 400 }
      )
    }

    // Create the category
    const category = await prisma.productCategory.create({
      data: {
        name,
        description: description || null,
        partner1Share: partner1Share || 20.0,
        partner2Share: partner2Share || 20.0,
        partner3Share: partner3Share || 20.0,
        partner4Share: partner4Share || 20.0,
        partner5Share: partner5Share || 20.0,
        isActive: isActive !== undefined ? isActive : true
      },
      include: {
        products: {
          select: {
            id: true,
            name: true,
            currentStock: true,
            isActive: true
          }
        }
      }
    })

    return NextResponse.json(category, { status: 201 })
  } catch (error) {
    console.error('Error creating category:', error)
    return NextResponse.json(
      { error: 'Failed to create category' },
      { status: 500 }
    )
  }
}

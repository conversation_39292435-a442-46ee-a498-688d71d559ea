const { PrismaClient } = require('../src/generated/prisma')

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create the 5 partners (you and your 4 friends)
  const partners = await Promise.all([
    prisma.partner.upsert({
      where: { name: 'Partner 1' },
      update: {},
      create: {
        name: 'Partner 1',
        email: '<EMAIL>',
        stake: 0.20,
        isActive: true,
      },
    }),
    prisma.partner.upsert({
      where: { name: 'Partner 2' },
      update: {},
      create: {
        name: 'Partner 2',
        email: '<EMAIL>',
        stake: 0.20,
        isActive: true,
      },
    }),
    prisma.partner.upsert({
      where: { name: 'Partner 3' },
      update: {},
      create: {
        name: 'Partner 3',
        email: '<EMAIL>',
        stake: 0.20,
        isActive: true,
      },
    }),
    prisma.partner.upsert({
      where: { name: 'Partner 4' },
      update: {},
      create: {
        name: 'Partner 4',
        email: '<EMAIL>',
        stake: 0.20,
        isActive: true,
      },
    }),
    prisma.partner.upsert({
      where: { name: 'Partner 5' },
      update: {},
      create: {
        name: 'Partner 5',
        email: '<EMAIL>',
        stake: 0.20,
        isActive: true,
      },
    }),
  ])

  console.log('✅ Created partners:', partners.map(p => p.name))

  // Create product categories with different profit sharing arrangements
  const categories = await Promise.all([
    prisma.productCategory.upsert({
      where: { name: 'Pokemon Cards' },
      update: {},
      create: {
        name: 'Pokemon Cards',
        description: 'Individual Pokemon trading cards',
        // High-value cards: Partner 1 gets more (expert in rare cards)
        partner1Share: 30.0,
        partner2Share: 20.0,
        partner3Share: 20.0,
        partner4Share: 15.0,
        partner5Share: 15.0,
      },
    }),
    prisma.productCategory.upsert({
      where: { name: 'Booster Packs' },
      update: {},
      create: {
        name: 'Booster Packs',
        description: 'Pokemon card booster packs',
        // Equal sharing for booster packs
        partner1Share: 20.0,
        partner2Share: 20.0,
        partner3Share: 20.0,
        partner4Share: 20.0,
        partner5Share: 20.0,
      },
    }),
    prisma.productCategory.upsert({
      where: { name: 'Deck Boxes' },
      update: {},
      create: {
        name: 'Deck Boxes',
        description: 'Card storage and deck boxes',
        // Partner 2 handles accessories sourcing
        partner1Share: 15.0,
        partner2Share: 35.0,
        partner3Share: 15.0,
        partner4Share: 17.5,
        partner5Share: 17.5,
      },
    }),
    prisma.productCategory.upsert({
      where: { name: 'Sleeves & Protectors' },
      update: {},
      create: {
        name: 'Sleeves & Protectors',
        description: 'Card sleeves and protective accessories',
        // Partner 2 handles accessories sourcing
        partner1Share: 15.0,
        partner2Share: 35.0,
        partner3Share: 15.0,
        partner4Share: 17.5,
        partner5Share: 17.5,
      },
    }),
    prisma.productCategory.upsert({
      where: { name: 'Accessories' },
      update: {},
      create: {
        name: 'Accessories',
        description: 'Other Pokemon-related accessories and merchandise',
        // Partner 3 handles general accessories
        partner1Share: 15.0,
        partner2Share: 15.0,
        partner3Share: 40.0,
        partner4Share: 15.0,
        partner5Share: 15.0,
      },
    }),
  ])

  console.log('✅ Created categories:', categories.map(c => c.name))

  // Create some sample products
  const products = await Promise.all([
    prisma.product.create({
      data: {
        name: 'Charizard VMAX (Secret Rare)',
        description: 'Charizard VMAX Secret Rare from Champion\'s Path',
        sku: 'CHAR-VMAX-SR-001',
        categoryId: categories[0].id, // Pokemon Cards
        costPrice: 45.00,
        salePrice: 75.00,
        currentStock: 2,
        minStock: 1,
      },
    }),
    prisma.product.create({
      data: {
        name: 'Champion\'s Path Booster Pack',
        description: 'Pokemon Champion\'s Path booster pack',
        sku: 'CP-BOOST-001',
        categoryId: categories[1].id, // Booster Packs
        costPrice: 3.50,
        salePrice: 6.00,
        currentStock: 24,
        minStock: 10,
      },
    }),
    prisma.product.create({
      data: {
        name: 'Ultra Pro Deck Box',
        description: 'Standard size deck box for Pokemon cards',
        sku: 'UP-DECK-001',
        categoryId: categories[2].id, // Deck Boxes
        costPrice: 8.00,
        salePrice: 15.00,
        currentStock: 12,
        minStock: 5,
      },
    }),
    prisma.product.create({
      data: {
        name: 'Dragon Shield Card Sleeves (100ct)',
        description: 'Premium card sleeves, pack of 100',
        sku: 'DS-SLEEVE-100',
        categoryId: categories[3].id, // Sleeves & Protectors
        costPrice: 12.00,
        salePrice: 20.00,
        currentStock: 8,
        minStock: 3,
      },
    }),
  ])

  console.log('✅ Created products:', products.map(p => p.name))

  console.log('🎉 Database seeded successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

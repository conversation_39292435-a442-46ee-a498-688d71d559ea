import Link from 'next/link'
import { prisma } from '@/lib/prisma'

async function getProductsData() {
  const [products, categories] = await Promise.all([
    prisma.product.findMany({
      include: { 
        category: true,
        _count: {
          select: { transactionItems: true }
        }
      },
      orderBy: { name: 'asc' }
    }),
    prisma.productCategory.findMany({
      include: {
        _count: { select: { products: true } }
      },
      orderBy: { name: 'asc' }
    })
  ])

  return { products, categories }
}

export default async function ProductsPage() {
  const { products, categories } = await getProductsData()

  const totalInventoryValue = products.reduce((sum, product) => 
    sum + (product.currentStock * product.costPrice), 0
  )

  const totalPotentialRevenue = products.reduce((sum, product) => 
    sum + (product.currentStock * product.salePrice), 0
  )

  const lowStockProducts = products.filter(p => p.currentStock <= p.minStock)

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <Link href="/" className="text-blue-600 hover:text-blue-800 mb-2 inline-block">
                ← Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                🎴 Product Inventory
              </h1>
              <p className="text-gray-600">
                Manage your Pokemon cards, booster packs, and accessories inventory
              </p>
            </div>
            <div className="flex space-x-3">
              <Link href="/categories" className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                Manage Categories
              </Link>
              <Link href="/products/new" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Add Product
              </Link>
            </div>
          </div>
        </div>

        {/* Inventory Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <span className="text-2xl">📦</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Products</p>
                <p className="text-2xl font-semibold text-gray-900">{products.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <span className="text-2xl">💰</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Inventory Value</p>
                <p className="text-2xl font-semibold text-gray-900">
                  ${totalInventoryValue.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <span className="text-2xl">💎</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Potential Revenue</p>
                <p className="text-2xl font-semibold text-gray-900">
                  ${totalPotentialRevenue.toFixed(2)}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="p-2 bg-red-100 rounded-lg">
                <span className="text-2xl">⚠️</span>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Low Stock</p>
                <p className="text-2xl font-semibold text-gray-900">{lowStockProducts.length}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Categories Filter */}
        <div className="bg-white rounded-lg shadow mb-6 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Product Categories</h2>
          <div className="flex flex-wrap gap-3">
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm">
              All Products ({products.length})
            </button>
            {categories.map((category) => (
              <button 
                key={category.id}
                className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-gray-200 transition-colors"
              >
                {category.name} ({category._count.products})
              </button>
            ))}
          </div>
        </div>

        {/* Low Stock Alert */}
        {lowStockProducts.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <span className="text-red-600 text-xl mr-3">⚠️</span>
              <div>
                <h3 className="text-red-800 font-medium">Low Stock Alert</h3>
                <p className="text-red-700 text-sm">
                  {lowStockProducts.length} product(s) are running low on stock and need restocking.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {products.map((product) => (
            <div key={product.id} className="bg-white rounded-lg shadow">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">
                      {product.name}
                    </h3>
                    <p className="text-sm text-gray-600 mb-2">{product.description}</p>
                    <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                      {product.category.name}
                    </span>
                  </div>
                  {product.currentStock <= product.minStock && (
                    <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                      Low Stock
                    </span>
                  )}
                </div>

                {/* Product Details */}
                <div className="space-y-3 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">SKU:</span>
                    <span className="font-medium">{product.sku || 'N/A'}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Cost Price:</span>
                    <span className="font-medium">${product.costPrice.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Sale Price:</span>
                    <span className="font-medium text-green-600">${product.salePrice.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Profit Margin:</span>
                    <span className="font-medium text-green-600">
                      ${(product.salePrice - product.costPrice).toFixed(2)}
                    </span>
                  </div>
                </div>

                {/* Category Profit Sharing */}
                <div className="bg-blue-50 rounded-lg p-3 mb-4">
                  <h4 className="text-xs font-medium text-blue-900 mb-2">Category Profit Sharing</h4>
                  <div className="grid grid-cols-5 gap-1 text-xs">
                    <div className="text-center">
                      <div className="text-blue-600 font-medium">P1</div>
                      <div className="text-blue-800">{product.category.partner1Share}%</div>
                    </div>
                    <div className="text-center">
                      <div className="text-blue-600 font-medium">P2</div>
                      <div className="text-blue-800">{product.category.partner2Share}%</div>
                    </div>
                    <div className="text-center">
                      <div className="text-blue-600 font-medium">P3</div>
                      <div className="text-blue-800">{product.category.partner3Share}%</div>
                    </div>
                    <div className="text-center">
                      <div className="text-blue-600 font-medium">P4</div>
                      <div className="text-blue-800">{product.category.partner4Share}%</div>
                    </div>
                    <div className="text-center">
                      <div className="text-blue-600 font-medium">P5</div>
                      <div className="text-blue-800">{product.category.partner5Share}%</div>
                    </div>
                  </div>
                </div>

                {/* Stock Information */}
                <div className="bg-gray-50 rounded-lg p-3 mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">Current Stock:</span>
                    <span className={`font-semibold ${
                      product.currentStock <= product.minStock ? 'text-red-600' : 'text-gray-900'
                    }`}>
                      {product.currentStock} units
                    </span>
                  </div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-600">Min Stock:</span>
                    <span className="text-sm text-gray-900">{product.minStock} units</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Total Value:</span>
                    <span className="font-semibold text-gray-900">
                      ${(product.currentStock * product.costPrice).toFixed(2)}
                    </span>
                  </div>
                </div>

                {/* Sales Information */}
                <div className="text-sm text-gray-600 mb-4">
                  <span>Sales: {product._count.transactionItems} transactions</span>
                </div>

                {/* Actions */}
                <div className="flex gap-2">
                  <Link href={`/products/${product.id}/edit`} className="flex-1 bg-blue-50 text-blue-600 px-3 py-2 rounded text-sm hover:bg-blue-100 transition-colors text-center">
                    Edit
                  </Link>
                  <button className="flex-1 bg-green-50 text-green-600 px-3 py-2 rounded text-sm hover:bg-green-100 transition-colors">
                    Restock
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Add Product Card */}
        <div className="mt-6">
          <div className="bg-white rounded-lg shadow border-2 border-dashed border-gray-300 p-8 text-center">
            <div className="text-4xl text-gray-400 mb-4">🎴</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Add New Product</h3>
            <p className="text-gray-600 mb-4">
              Add Pokemon cards, booster packs, or accessories to your inventory
            </p>
            <Link href="/products/new" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Add Product
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

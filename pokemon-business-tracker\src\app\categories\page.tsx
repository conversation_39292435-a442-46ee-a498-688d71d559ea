'use client'

import Link from 'next/link'
import { useState, useEffect } from 'react'

interface Product {
  id: string
  name: string
  currentStock: number
  isActive: boolean
}

interface Category {
  id: string
  name: string
  description?: string
  partner1Share: number
  partner2Share: number
  partner3Share: number
  partner4Share: number
  partner5Share: number
  isActive: boolean
  products: Product[]
  createdAt: string
  updatedAt: string
}

async function fetchCategories(): Promise<Category[]> {
  const response = await fetch('/api/categories')
  if (!response.ok) {
    throw new Error('Failed to fetch categories')
  }
  return response.json()
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const data = await fetchCategories()
        setCategories(data)
      } catch (error) {
        console.error('Error loading categories:', error)
      } finally {
        setLoading(false)
      }
    }

    loadCategories()
  }, [])

  const getPartnerShareColor = (share: number) => {
    if (share >= 30) return 'text-green-600 font-semibold'
    if (share >= 25) return 'text-blue-600 font-medium'
    if (share >= 20) return 'text-gray-700'
    return 'text-gray-500'
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading categories...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <Link href="/" className="text-blue-600 hover:text-blue-800 mb-2 inline-block">
                ← Back to Dashboard
              </Link>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                📂 Product Categories
              </h1>
              <p className="text-gray-600">
                Manage product categories and partner profit-sharing arrangements
              </p>
            </div>
            <Link href="/categories/new" className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              New Category
            </Link>
          </div>
        </div>

        {/* Categories Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {categories.length > 0 ? (
            categories.map((category) => (
              <div key={category.id} className="bg-white rounded-lg shadow hover:shadow-md transition-shadow">
                <div className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900">
                      {category.name}
                    </h3>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      category.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {category.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </div>
                  
                  {category.description && (
                    <p className="text-gray-600 text-sm mb-4">{category.description}</p>
                  )}

                  {/* Partner Profit Shares */}
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">Partner Profit Shares</h4>
                    <div className="grid grid-cols-5 gap-1 text-xs">
                      <div className="text-center">
                        <div className="text-gray-500">P1</div>
                        <div className={getPartnerShareColor(category.partner1Share)}>
                          {category.partner1Share}%
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-500">P2</div>
                        <div className={getPartnerShareColor(category.partner2Share)}>
                          {category.partner2Share}%
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-500">P3</div>
                        <div className={getPartnerShareColor(category.partner3Share)}>
                          {category.partner3Share}%
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-500">P4</div>
                        <div className={getPartnerShareColor(category.partner4Share)}>
                          {category.partner4Share}%
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="text-gray-500">P5</div>
                        <div className={getPartnerShareColor(category.partner5Share)}>
                          {category.partner5Share}%
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Products Count */}
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <span>{category.products.length} products</span>
                    <span>
                      {category.products.filter(p => p.isActive).length} active
                    </span>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2">
                    <Link 
                      href={`/categories/${category.id}`}
                      className="flex-1 bg-blue-600 text-white text-center py-2 px-3 rounded text-sm hover:bg-blue-700 transition-colors"
                    >
                      View Details
                    </Link>
                    <Link 
                      href={`/categories/${category.id}/edit`}
                      className="flex-1 bg-gray-100 text-gray-700 text-center py-2 px-3 rounded text-sm hover:bg-gray-200 transition-colors"
                    >
                      Edit
                    </Link>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="col-span-full">
              <div className="bg-white rounded-lg shadow p-12 text-center">
                <div className="text-4xl text-gray-400 mb-4">📂</div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">No categories yet</h3>
                <p className="text-gray-600 mb-4">
                  Create your first product category to organize your Pokemon card inventory
                </p>
                <Link href="/categories/new" className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Create First Category
                </Link>
              </div>
            </div>
          )}
        </div>

        {/* Summary Stats */}
        {categories.length > 0 && (
          <div className="mt-8 bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Category Summary</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{categories.length}</div>
                <div className="text-sm text-gray-600">Total Categories</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {categories.filter(c => c.isActive).length}
                </div>
                <div className="text-sm text-gray-600">Active Categories</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {categories.reduce((sum, c) => sum + c.products.length, 0)}
                </div>
                <div className="text-sm text-gray-600">Total Products</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-600">
                  {categories.reduce((sum, c) => sum + c.products.filter(p => p.isActive).length, 0)}
                </div>
                <div className="text-sm text-gray-600">Active Products</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

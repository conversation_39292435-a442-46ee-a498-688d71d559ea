'use client'

import Link from 'next/link'
import { useState } from 'react'
import { useRouter } from 'next/navigation'

export default function NewCategoryPage() {
  const router = useRouter()
  const [submitting, setSubmitting] = useState(false)

  // Form state
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [partner1Share, setPartner1Share] = useState(20)
  const [partner2Share, setPartner2Share] = useState(20)
  const [partner3Share, setPartner3Share] = useState(20)
  const [partner4Share, setPartner4Share] = useState(20)
  const [partner5Share, setPartner5Share] = useState(20)
  const [isActive, setIsActive] = useState(true)

  const totalShare = partner1Share + partner2Share + partner3Share + partner4Share + partner5Share
  const isValidShare = Math.abs(totalShare - 100) < 0.01

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!isValidShare) {
      alert('Partner profit shares must add up to 100%')
      return
    }

    setSubmitting(true)

    try {
      const categoryData = {
        name,
        description,
        partner1Share,
        partner2Share,
        partner3Share,
        partner4Share,
        partner5Share,
        isActive
      }

      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(categoryData),
      })

      if (response.ok) {
        router.push('/categories')
      } else {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create category')
      }
    } catch (error) {
      console.error('Error creating category:', error)
      alert(`Error creating category: ${error instanceof Error ? error.message : 'Please try again.'}`)
    } finally {
      setSubmitting(false)
    }
  }

  const handleShareChange = (partner: number, value: number) => {
    switch (partner) {
      case 1: setPartner1Share(value); break
      case 2: setPartner2Share(value); break
      case 3: setPartner3Share(value); break
      case 4: setPartner4Share(value); break
      case 5: setPartner5Share(value); break
    }
  }

  const distributeEqually = () => {
    setPartner1Share(20)
    setPartner2Share(20)
    setPartner3Share(20)
    setPartner4Share(20)
    setPartner5Share(20)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <Link href="/categories" className="text-blue-600 hover:text-blue-800 mb-2 inline-block">
            ← Back to Categories
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            📂 New Product Category
          </h1>
          <p className="text-gray-600">
            Create a new product category with partner profit-sharing arrangement
          </p>
        </div>

        {/* Form */}
        <div className="bg-white rounded-lg shadow p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h2>
              
              <div className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                    Category Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., Pokemon Cards, Booster Packs, Accessories"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                    Description
                  </label>
                  <textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Brief description of this product category"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isActive"
                    checked={isActive}
                    onChange={(e) => setIsActive(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                    Active category
                  </label>
                </div>
              </div>
            </div>

            {/* Partner Profit Sharing */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Partner Profit Sharing</h2>
                <button
                  type="button"
                  onClick={distributeEqually}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  Distribute Equally
                </button>
              </div>
              
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map((partnerNum) => (
                  <div key={partnerNum} className="flex items-center space-x-4">
                    <label className="w-20 text-sm font-medium text-gray-700">
                      Partner {partnerNum}
                    </label>
                    <div className="flex-1">
                      <input
                        type="number"
                        min="0"
                        max="100"
                        step="0.1"
                        value={
                          partnerNum === 1 ? partner1Share :
                          partnerNum === 2 ? partner2Share :
                          partnerNum === 3 ? partner3Share :
                          partnerNum === 4 ? partner4Share :
                          partner5Share
                        }
                        onChange={(e) => handleShareChange(partnerNum, parseFloat(e.target.value) || 0)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <span className="w-8 text-sm text-gray-500">%</span>
                  </div>
                ))}
              </div>

              {/* Total Share Validation */}
              <div className="mt-4 p-3 rounded-lg bg-gray-50">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-700">Total Share:</span>
                  <span className={`text-sm font-semibold ${
                    isValidShare ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {totalShare.toFixed(1)}%
                  </span>
                </div>
                {!isValidShare && (
                  <p className="text-xs text-red-600 mt-1">
                    Partner shares must add up to exactly 100%
                  </p>
                )}
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex space-x-4">
              <button
                type="submit"
                disabled={submitting || !isValidShare || !name.trim()}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {submitting ? 'Creating...' : 'Create Category'}
              </button>
              <Link
                href="/categories"
                className="flex-1 bg-gray-100 text-gray-700 text-center py-2 px-4 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancel
              </Link>
            </div>
          </form>
        </div>

        {/* Help Text */}
        <div className="mt-6 bg-blue-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-blue-900 mb-2">💡 Profit Sharing Tips</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Different categories can have different profit-sharing arrangements</li>
            <li>• Consider expertise: Give higher shares to partners who specialize in certain categories</li>
            <li>• All partner shares must add up to exactly 100%</li>
            <li>• You can change these percentages later by editing the category</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

interface Partner {
  id: string
  name: string
}

interface Product {
  id: string
  name: string
  salePrice: number
  currentStock: number
  category: {
    name: string
  }
}

interface TransactionItem {
  productId?: string // Optional for freeform products
  quantity: number
  unitPrice: number

  // For freeform products
  freeformName?: string
  freeformDescription?: string
  freeformSku?: string

  // Trade-in information
  isTradeIn?: boolean
  tradeInValue?: number
  tradeInCondition?: string
  tradeInNotes?: string
}

interface FreeformProduct {
  name: string
  description: string
  sku: string
  unitPrice: number
}

export default function NewTransactionPage() {
  const router = useRouter()
  const [partners, setPartners] = useState<Partner[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)

  // Form state
  const [transactionType, setTransactionType] = useState<'SALE' | 'PURCHASE' | 'EXPENSE'>('SALE')
  const [description, setDescription] = useState('')
  const [partnerId, setPartnerId] = useState('')
  const [notes, setNotes] = useState('')
  const [items, setItems] = useState<TransactionItem[]>([])
  const [tradeInItems, setTradeInItems] = useState<TransactionItem[]>([])
  const [expenseAmount, setExpenseAmount] = useState('')

  // UI state
  const [showFreeformProduct, setShowFreeformProduct] = useState(false)
  const [showTradeInSection, setShowTradeInSection] = useState(false)

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [partnersRes, productsRes] = await Promise.all([
        fetch('/api/partners'),
        fetch('/api/products')
      ])
      
      const partnersData = await partnersRes.json()
      const productsData = await productsRes.json()
      
      setPartners(partnersData)
      setProducts(productsData)
    } catch (error) {
      console.error('Error fetching data:', error)
    } finally {
      setLoading(false)
    }
  }

  const addItem = () => {
    setItems([...items, { productId: '', quantity: 1, unitPrice: 0 }])
  }

  const addFreeformItem = () => {
    setItems([...items, {
      quantity: 1,
      unitPrice: 0,
      freeformName: '',
      freeformDescription: '',
      freeformSku: ''
    }])
    setShowFreeformProduct(false)
  }

  const addTradeInItem = () => {
    setTradeInItems([...tradeInItems, {
      quantity: 1,
      unitPrice: 0,
      isTradeIn: true,
      tradeInValue: 0,
      tradeInCondition: 'Good',
      freeformName: '',
      freeformDescription: ''
    }])
  }

  const removeItem = (index: number) => {
    setItems(items.filter((_, i) => i !== index))
  }

  const removeTradeInItem = (index: number) => {
    setTradeInItems(tradeInItems.filter((_, i) => i !== index))
  }

  const updateItem = (index: number, field: keyof TransactionItem, value: string | number) => {
    const updatedItems = [...items]
    updatedItems[index] = { ...updatedItems[index], [field]: value }

    // Auto-populate price when product is selected
    if (field === 'productId' && value) {
      const product = getProductById(value as string)
      if (product) {
        updatedItems[index].unitPrice = product.salePrice
      }
    }

    setItems(updatedItems)
  }

  const updateTradeInItem = (index: number, field: keyof TransactionItem, value: string | number) => {
    const updatedItems = [...tradeInItems]
    updatedItems[index] = { ...updatedItems[index], [field]: value }
    setTradeInItems(updatedItems)
  }

  const getProductById = (productId: string) => {
    return products.find(p => p.id === productId)
  }

  const calculateTotal = () => {
    if (transactionType === 'EXPENSE') {
      return parseFloat(expenseAmount) || 0
    }
    const itemsTotal = items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0)
    const tradeInTotal = tradeInItems.reduce((sum, item) => sum + (item.tradeInValue || 0), 0)
    return itemsTotal - tradeInTotal // Subtract trade-in value from total
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      // Combine regular items and trade-in items
      const allItems = [
        ...items.filter(item => item.productId || item.freeformName), // Include freeform items
        ...tradeInItems.map(item => ({ ...item, isTradeIn: true }))
      ]

      const transactionData = {
        type: transactionType,
        description,
        partnerId: partnerId || null,
        notes,
        totalAmount: calculateTotal(),
        items: transactionType !== 'EXPENSE' ? allItems : []
      }

      const response = await fetch('/api/transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(transactionData),
      })

      if (response.ok) {
        router.push('/transactions')
      } else {
        throw new Error('Failed to create transaction')
      }
    } catch (error) {
      console.error('Error creating transaction:', error)
      alert('Error creating transaction. Please try again.')
    } finally {
      setSubmitting(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <Link href="/transactions" className="text-blue-600 hover:text-blue-800 mb-2 inline-block">
            ← Back to Transactions
          </Link>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            💳 New Transaction
          </h1>
          <p className="text-gray-600">
            Record a sale, purchase, or business expense
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Transaction Type */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Transaction Type</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {[
                { value: 'SALE', label: 'Sale', icon: '💰', desc: 'Record a sale to customers' },
                { value: 'PURCHASE', label: 'Purchase', icon: '🛒', desc: 'Record inventory purchases' },
                { value: 'EXPENSE', label: 'Expense', icon: '💸', desc: 'Record business expenses' }
              ].map((type) => (
                <label key={type.value} className="cursor-pointer">
                  <input
                    type="radio"
                    name="transactionType"
                    value={type.value}
                    checked={transactionType === type.value}
                    onChange={(e) => setTransactionType(e.target.value as any)}
                    className="sr-only"
                  />
                  <div className={`border-2 rounded-lg p-4 text-center transition-colors ${
                    transactionType === type.value 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <div className="text-2xl mb-2">{type.icon}</div>
                    <div className="font-medium text-gray-900">{type.label}</div>
                    <div className="text-sm text-gray-600">{type.desc}</div>
                  </div>
                </label>
              ))}
            </div>
          </div>

          {/* Basic Information */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Transaction Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Description *
                </label>
                <input
                  type="text"
                  required
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder={`Enter ${transactionType.toLowerCase()} description`}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Partner
                </label>
                <select
                  value={partnerId}
                  onChange={(e) => setPartnerId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
                >
                  <option value="">Select partner (optional)</option>
                  {partners.map((partner) => (
                    <option key={partner.id} value={partner.id}>
                      {partner.name}
                    </option>
                  ))}
                </select>
              </div>
            </div>
            <div className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Notes
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                placeholder="Additional notes (optional)"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
              />
            </div>
          </div>

          {/* Items or Expense Amount */}
          {transactionType === 'EXPENSE' ? (
            <div className="bg-white rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Expense Amount</h2>
              <div className="max-w-xs">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Amount *
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">$</span>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    required
                    value={expenseAmount}
                    onChange={(e) => setExpenseAmount(e.target.value)}
                    placeholder="0.00"
                    className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
                  />
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Items</h2>
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={addItem}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Add Product
                  </button>
                  <button
                    type="button"
                    onClick={addFreeformItem}
                    className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition-colors"
                  >
                    Add Custom Item
                  </button>
                  {transactionType === 'SALE' && (
                    <button
                      type="button"
                      onClick={() => setShowTradeInSection(!showTradeInSection)}
                      className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
                    >
                      {showTradeInSection ? 'Hide' : 'Add'} Trade-ins
                    </button>
                  )}
                </div>
              </div>
              
              {items.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  No items added yet. Click "Add Product" or "Add Custom Item" to get started.
                </p>
              ) : (
                <div className="space-y-4">
                  {items.map((item, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-4">
                        <h3 className="text-sm font-medium text-gray-700">
                          {item.freeformName ? 'Custom Item' : 'Product Item'} #{index + 1}
                        </h3>
                        <button
                          type="button"
                          onClick={() => removeItem(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          Remove
                        </button>
                      </div>

                      {item.freeformName ? (
                        // Freeform product fields
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Product Name *
                            </label>
                            <input
                              type="text"
                              value={item.freeformName || ''}
                              onChange={(e) => updateItem(index, 'freeformName', e.target.value)}
                              placeholder="e.g., Charizard Base Set"
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
                            />
                          </div>
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              SKU (Optional)
                            </label>
                            <input
                              type="text"
                              value={item.freeformSku || ''}
                              onChange={(e) => updateItem(index, 'freeformSku', e.target.value)}
                              placeholder="e.g., CHAR-BS-001"
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
                            />
                          </div>
                          <div className="md:col-span-2">
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Description (Optional)
                            </label>
                            <input
                              type="text"
                              value={item.freeformDescription || ''}
                              onChange={(e) => updateItem(index, 'freeformDescription', e.target.value)}
                              placeholder="Brief description of the item"
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
                            />
                          </div>
                        </div>
                      ) : (
                        // Regular product selection
                        <div className="mb-4">
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Product
                          </label>
                          <select
                            value={item.productId || ''}
                            onChange={(e) => updateItem(index, 'productId', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
                          >
                            <option value="">Select product</option>
                            {products.map((product) => (
                              <option key={product.id} value={product.id}>
                                {product.name} ({product.category.name}) - Stock: {product.currentStock}
                              </option>
                            ))}
                          </select>
                        </div>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Quantity
                          </label>
                          <input
                            type="number"
                            min="1"
                            value={item.quantity}
                            onChange={(e) => updateItem(index, 'quantity', parseInt(e.target.value) || 1)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Unit Price *
                            {item.productId && (
                              <span className="text-xs text-gray-500 ml-1">
                                (Default: ${getProductById(item.productId)?.salePrice.toFixed(2) || '0.00'})
                              </span>
                            )}
                          </label>
                          <div className="relative">
                            <span className="absolute left-3 top-2 text-gray-500">$</span>
                            <input
                              type="number"
                              step="0.01"
                              min="0"
                              value={item.unitPrice}
                              onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                              placeholder={item.productId ? getProductById(item.productId)?.salePrice.toFixed(2) : '0.00'}
                              className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900 bg-white"
                            />
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Total
                          </label>
                          <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-gray-900 font-medium">
                            ${(item.quantity * item.unitPrice).toFixed(2)}
                          </div>
                        </div>
                      </div>

                      {item.productId && getProductById(item.productId) && (
                        <div className="mt-2 text-sm text-gray-600 bg-blue-50 p-2 rounded">
                          <strong>Product Info:</strong> Stock: {getProductById(item.productId)!.currentStock} available
                          {item.unitPrice !== getProductById(item.productId)!.salePrice && (
                            <span className="ml-4 text-orange-600">
                              (Price overridden from ${getProductById(item.productId)!.salePrice.toFixed(2)})
                            </span>
                          )}
                        </div>
                      )}

                      {item.freeformName && (
                        <div className="mt-2 text-sm text-gray-600 bg-green-50 p-2 rounded">
                          <strong>Custom Item:</strong> {item.freeformName}
                          {item.freeformDescription && ` - ${item.freeformDescription}`}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Trade-in Section */}
          {transactionType === 'SALE' && showTradeInSection && (
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold text-gray-900">Trade-ins</h2>
                <button
                  type="button"
                  onClick={addTradeInItem}
                  className="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 transition-colors"
                >
                  Add Trade-in Item
                </button>
              </div>

              {tradeInItems.length === 0 ? (
                <p className="text-gray-500 text-center py-8">
                  No trade-in items added yet. Click "Add Trade-in Item" to get started.
                </p>
              ) : (
                <div className="space-y-4">
                  {tradeInItems.map((item, index) => (
                    <div key={index} className="border border-purple-200 rounded-lg p-4 bg-purple-50">
                      <div className="flex justify-between items-start mb-4">
                        <h3 className="text-sm font-medium text-purple-700">
                          Trade-in Item #{index + 1}
                        </h3>
                        <button
                          type="button"
                          onClick={() => removeTradeInItem(index)}
                          className="text-red-600 hover:text-red-800"
                        >
                          Remove
                        </button>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Item Name *
                          </label>
                          <input
                            type="text"
                            value={item.freeformName || ''}
                            onChange={(e) => updateTradeInItem(index, 'freeformName', e.target.value)}
                            placeholder="e.g., Pikachu Base Set"
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Condition
                          </label>
                          <select
                            value={item.tradeInCondition || 'Good'}
                            onChange={(e) => updateTradeInItem(index, 'tradeInCondition', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
                          >
                            <option value="Mint">Mint</option>
                            <option value="Near Mint">Near Mint</option>
                            <option value="Excellent">Excellent</option>
                            <option value="Good">Good</option>
                            <option value="Fair">Fair</option>
                            <option value="Poor">Poor</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Trade-in Value *
                          </label>
                          <div className="relative">
                            <span className="absolute left-3 top-2 text-gray-500">$</span>
                            <input
                              type="number"
                              step="0.01"
                              min="0"
                              value={item.tradeInValue || 0}
                              onChange={(e) => updateTradeInItem(index, 'tradeInValue', parseFloat(e.target.value) || 0)}
                              className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
                            />
                          </div>
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Notes (Optional)
                          </label>
                          <input
                            type="text"
                            value={item.tradeInNotes || ''}
                            onChange={(e) => updateTradeInItem(index, 'tradeInNotes', e.target.value)}
                            placeholder="Additional notes about condition, etc."
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 text-gray-900 bg-white"
                          />
                        </div>
                      </div>

                      <div className="text-sm text-purple-700 bg-purple-100 p-2 rounded">
                        <strong>Trade-in Credit:</strong> ${(item.tradeInValue || 0).toFixed(2)}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Total */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Transaction Summary</h2>

            {transactionType === 'SALE' && tradeInItems.length > 0 ? (
              <div className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-gray-600">Items Subtotal:</span>
                  <span className="font-medium">
                    ${items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0).toFixed(2)}
                  </span>
                </div>
                <div className="flex justify-between text-purple-600">
                  <span>Trade-in Credit:</span>
                  <span className="font-medium">
                    -${tradeInItems.reduce((sum, item) => sum + (item.tradeInValue || 0), 0).toFixed(2)}
                  </span>
                </div>
                <div className="border-t pt-2 flex justify-between items-center">
                  <span className="text-lg font-semibold text-gray-900">Total Amount Due:</span>
                  <span className="text-2xl font-bold text-gray-900">
                    ${calculateTotal().toFixed(2)}
                  </span>
                </div>
              </div>
            ) : (
              <div className="flex justify-between items-center">
                <span className="text-lg font-semibold text-gray-900">Total Amount:</span>
                <span className="text-2xl font-bold text-gray-900">
                  ${calculateTotal().toFixed(2)}
                </span>
              </div>
            )}
          </div>

          {/* Submit */}
          <div className="flex gap-4">
            <Link
              href="/transactions"
              className="flex-1 bg-gray-100 text-gray-700 px-6 py-3 rounded-lg text-center hover:bg-gray-200 transition-colors"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={submitting || calculateTotal() === 0}
              className="flex-1 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {submitting ? 'Creating...' : `Create ${transactionType}`}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
